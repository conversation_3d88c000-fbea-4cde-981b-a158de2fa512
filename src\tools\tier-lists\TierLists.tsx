'use client';

import { useState } from 'react';
import { tierListsData } from './data';
import { TierListType } from './types';
import { TIER_LIST_TABS } from './config';
import TierListSection from './components/TierListSection';

export default function TierLists() {
  const [activeTab, setActiveTab] = useState<TierListType>('single-target');

  return (
    <div className="text-gray-200">
      <div className="container mx-auto max-w-8xl p-2 sm:p-4 md:p-5 lg:p-6">
        {/* Header Section */}
        <div className="component-bg-dark mb-3 sm:mb-4 lg:mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 mb-2">
            <div className="flex items-center gap-4">
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-game-gold glow-text-lg">Tier Lists</h1>
            </div>
          </div>
          <p className="text-foreground/80 leading-relaxed">
            Detailed Cabal Online Class Tier Lists ranking the performance of Warrior, Wizard, Blader, Dark Mage, Force Archer, Force Gunner, Force Blader, and Gladiator. The rankings aim to provide insights into optimal class choices for both Player vs. Environment (PvE) and Player vs. Player (PvP) effectiveness, considering factors like single-target damage, Area of Effect (AoE) capabilities.
          </p>
        </div>

        {/* Main Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
          {/* Sidebar - Tier List Types */}
          <div className="lg:col-span-1 component-bg">
            <div className="p-3 sm:p-4">
              <h2 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 text-game-gold glow-text-sm">Tier Lists</h2>
              
              <div className="space-y-2">
                {TIER_LIST_TABS.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      w-full flex items-center p-3 sm:p-4 rounded-lg transition-all duration-200
                      ${activeTab === tab.id 
                        ? 'glass-panel border-game-highlight glow-border text-white' 
                        : 'glass-panel-light text-gray-300 hover:border-border-light hover:text-white'
                      }
                    `}
                  >
                    <div className="text-left">
                      <div className="font-medium text-sm sm:text-base">
                        {tab.label}
                      </div>
                      <div className="text-xs opacity-75 hidden sm:block">
                        {tab.description}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3 space-y-3 sm:space-y-4 lg:space-y-6 component-bg-light bg-pattern-grid">
            {/* Active Tier List */} 
            <div className="glass-panel-dark shadow-game p-3 sm:p-4 lg:p-6 mb-3 sm:mb-4 lg:mb-6">
              <TierListSection tierListData={tierListsData[activeTab]} />
            </div>

            {/* Footer Note */}
            <div className="text-center">
              <p className="text-foreground/60 text-sm">
                Click on any tier row to expand detailed information about each class.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}