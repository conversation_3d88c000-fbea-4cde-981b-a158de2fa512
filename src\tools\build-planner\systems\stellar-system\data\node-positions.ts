/**
 * Node Positions Data
 * This file contains the coordinate positions for all stellar nodes
 */

export interface NodePosition {
  x: number;
  y: number;
}

// Node positions mapped by node ID
export const nodePositions: Record<number, NodePosition> = {
  1: { x: 0.5336658354114713, y: 0.12295433291770573 },
  2: { x: 0.5972568578553616, y: 0.23642066708229426 },
  3: { x: 0.5024937655860349, y: 0.314974283042394 },
  4: { x: 0.5037406483790524, y: 0.40849049251870323 },
  5: { x: 0.8354114713216958, y: 0.2788146820448878 },
  6: { x: 0.7942643391521197, y: 0.39602166458852867 },
  7: { x: 0.7144638403990025, y: 0.29627104114713215 },
  8: { x: 0.6246882793017456, y: 0.34988700124688277 },
  9: { x: 0.699501246882793, y: 0.43966256234413964 },
  10: { x: 0.6022443890274314, y: 0.4695877493765586 },
  11: { x: 0.7768079800498753, y: 0.7750740336658354 },
  12: { x: 0.7518703241895262, y: 0.6703358790523691 },
  13: { x: 0.8927680798004988, y: 0.6266949812967582 },
  14: { x: 0.8042394014962594, y: 0.5643508416458853 },
  15: { x: 0.9164588528678305, y: 0.44215632793017456 },
  16: { x: 0.7094763092269327, y: 0.5344256546134664 },
  17: { x: 0.6296758104738155, y: 0.64539822319202 },
  18: { x: 0.5648379052369077, y: 0.5705852556109726 },
  19: { x: 0.6234413965087282, y: 0.8486401184538653 },
  20: { x: 0.6234413965087282, y: 0.7501363778054863 },
  21: { x: 0.5124688279301746, y: 0.6828047069825436 },
  22: { x: 0.48129675810473815, y: 0.8636027119700748 },
  23: { x: 0.40523690773067333, y: 0.7613583229426434 },
  24: { x: 0.2967581047381546, y: 0.8162211658354115 },
  25: { x: 0.31546134663341646, y: 0.7214580735660848 },
  26: { x: 0.16832917705735662, y: 0.7152236596009975 },
  27: { x: 0.3802992518703242, y: 0.6441513403990025 },
  28: { x: 0.442643391521197, y: 0.5730790211970075 },
  29: { x: 0.32418952618453867, y: 0.16784211346633415 },
  30: { x: 0.4401496259351621, y: 0.22769248753117208 },
  31: { x: 0.38403990024937656, y: 0.3536276496259352 },
  32: { x: 0.3192019950124688, y: 0.28006156483790523 },
  33: { x: 0.18952618453865336, y: 0.2613583229426434 },
  34: { x: 0.2194513715710723, y: 0.3910341334164589 },
  35: { x: 0.09850374064837905, y: 0.44839074189526185 },
  36: { x: 0.1970074812967581, y: 0.516969295511222 },
  37: { x: 0.11221945137157108, y: 0.6179668017456359 },
  38: { x: 0.30798004987531175, y: 0.5606101932668329 },
  39: { x: 0.30673316708229426, y: 0.43966256234413964 },
  40: { x: 0.40648379052369077, y: 0.47208151496259354 }
};

// Helper function to get constellation from node ID
export const getConstellationFromId = (id: number): string => {
  if (id <= 4) return 'daedalus';
  if (id <= 10) return 'icarus';
  if (id <= 18) return 'vulcanos';
  if (id <= 28) return 'minerva';
  return 'pluto';
};