/**
 * Batch Calculate Modal Component
 * Modal for calculating materials needed for batch crafting with full analysis
 */

'use client';

import React, { useState } from 'react';
import { ChloeRecipe } from '../data/recipes';
import { ProfitCalculations, BatchResults } from './ProfitCalculations';

interface BatchCalculateModalProps {
  isOpen: boolean;
  recipe: ChloeRecipe | null;
  batchQuantity: number;
  setBatchQuantity: (value: number) => void;
  excludeRegisterCost: boolean;
  setExcludeRegisterCost: (value: boolean) => void;
  getPrice: (name: string) => number | null;
  salesFee: number;
  onClose: () => void;
}

export default function BatchCalculateModal({
  isOpen,
  recipe,
  batchQuantity,
  setBatchQuantity,
  excludeRegisterCost,
  setExcludeRegisterCost,
  getPrice,
  salesFee,
  onClose
}: BatchCalculateModalProps) {
  const [showResults, setShowResults] = useState(false);
  const [batchResults, setBatchResults] = useState<BatchResults | null>(null);

  if (!isOpen || !recipe) return null;

  const handleCalculate = () => {
    const results = ProfitCalculations.calculateBatchResults(
      recipe,
      batchQuantity,
      getPrice,
      salesFee,
      excludeRegisterCost
    );
    setBatchResults(results);
    setShowResults(true);
  };

  const handleClose = () => {
    setShowResults(false);
    setBatchResults(null);
    onClose();
  };

  const profitStatusClass = batchResults ? ProfitCalculations.getProfitStatus(batchResults.profitMargin) : '';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-theme-darker border border-border-dark rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-bold">Batch Calculation: {recipe.recipe}</h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ×
          </button>
        </div>
        
        <div className="space-y-4">
          {/* Input Controls */}
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <label className="text-sm text-gray-300">Number of Crafts:</label>
              <input
                type="number"
                min="1"
                value={batchQuantity}
                onChange={(e) => setBatchQuantity(Number(e.target.value) || 1)}
                className="w-20 bg-theme-dark border border-border-dark rounded px-2 py-1 text-white focus:border-blue-500 focus:outline-none [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-inner-spin-button]:m-0 [&::-webkit-outer-spin-button]:m-0 [-moz-appearance:textfield]"
              />
            </div>
            
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={excludeRegisterCost}
                onChange={(e) => setExcludeRegisterCost(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-gray-300">Exclude register cost (recipe already unlocked)</span>
            </label>
            
            <button
              onClick={handleCalculate}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Calculate
            </button>
          </div>

          {/* Results Section */}
          {showResults && batchResults && (
            <div className="space-y-6">
              {/* Materials List */}
              <div className="bg-theme-dark rounded-lg p-4">
                <h4 className="font-medium mb-3">Materials Needed:</h4>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-border-dark">
                        <th className="text-left py-2 text-gray-300">Item</th>
                        <th className="text-right py-2 text-gray-300">Quantity</th>
                        <th className="text-right py-2 text-gray-300">Price per unit</th>
                        <th className="text-right py-2 text-gray-300">Total Cost</th>
                      </tr>
                    </thead>
                    <tbody>
                      {batchResults.materials.map((material, index) => (
                        <tr key={`batch-material-${index}`} className="border-b border-border-dark/50">
                          <td className="py-2 text-white">{material.name}</td>
                          <td className="py-2 text-right text-blue-400 font-medium">
                            {ProfitCalculations.formatNumber(material.quantity)}
                          </td>
                          <td className="py-2 text-right text-gray-300">
                            {ProfitCalculations.formatNumber(material.price)} Alz
                          </td>
                          <td className="py-2 text-right text-yellow-400 font-medium">
                            {ProfitCalculations.formatNumber(material.total)} Alz
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Summary Section */}
              <div className="bg-theme-dark rounded-lg p-4">
                <h4 className="font-medium mb-3">Batch Summary:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-300">Total Crafting Cost:</span>
                      <span className="text-red-400">{ProfitCalculations.formatNumber(batchResults.totalCraftingCost)} Alz</span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-300">Register Cost (one-time):</span>
                      <span className="text-yellow-400">
                        {ProfitCalculations.formatNumber(batchResults.registerCost)} Alz 
                        <span className="text-gray-500 ml-1">
                          {excludeRegisterCost ? '(excluded)' : '(included)'}
                        </span>
                      </span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-300">Expected Output:</span>
                      <span className="text-blue-400">
                        {batchResults.expectedOutputQuantity.toFixed(1)} items ({recipe.successRate}% success)
                      </span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-300">Expected Revenue:</span>
                      <span className="text-green-400">{ProfitCalculations.formatNumber(batchResults.expectedRevenue)} Alz</span>
                    </div>
                    
                    <div className={`flex justify-between ${profitStatusClass === 'profit-negative' ? 'text-red-400' : 
                                                          profitStatusClass === 'profit-low' ? 'text-yellow-400' :
                                                          profitStatusClass === 'profit-medium' ? 'text-blue-400' : 'text-green-400'}`}>
                      <span className="text-gray-300">Expected Profit:</span>
                      <span>{ProfitCalculations.formatNumber(batchResults.expectedProfit)} Alz</span>
                    </div>
                    
                    <div className={`flex justify-between ${profitStatusClass === 'profit-negative' ? 'text-red-400' : 
                                                          profitStatusClass === 'profit-low' ? 'text-yellow-400' :
                                                          profitStatusClass === 'profit-medium' ? 'text-blue-400' : 'text-green-400'}`}>
                      <span className="text-gray-300">Profit Margin:</span>
                      <span>{batchResults.profitMargin.toFixed(2)}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}