# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js build output
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# documentation
docs/

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.vercel/
.idea/
.vscode/
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# kiro folder
.kiro/

#zencoder folder
.zencoder

# typescript
*.tsbuildinfo
next-env.d.ts

# WordPress site content (for migration reference only)
MY_WORDPRESS_SITE_CONTENT/
