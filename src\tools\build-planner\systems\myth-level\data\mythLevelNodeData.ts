// Mythical Level System Node Data
// Comprehensive data structure for all myth level nodes with flexible stat levels and holy power

import { getStatInfo } from '../../../data/stats-config';

// Individual level data for a stat
export interface StatLevelData {
  level: number;        // 1, 2, 3, etc.
  value: number;        // Actual stat value at this level
  holyPower: number;    // Holy power gained when rolling this level
}

// Complete stat configuration for a node
export interface MythNodeStat {
  statKey: string;                    // Key from stats-config.ts
  name: string;                       // Display name (from stats-config)
  category: 'offensive' | 'defensive' | 'utility';
  isPercentage: boolean;              // From stats-config
  description: string;                // From stats-config
  icon?: string;                      // Icon path (from stats-config)
  maxLevel: number;                   // Maximum level for this stat in this node
  levels: StatLevelData[];            // Array of level data (1 to maxLevel)
  minValue: number;                   // Minimum possible value (level 1)
  maxValue: number;                   // Maximum possible value (max level)
}

// Complete node configuration
export interface MythLevelNodeData {
  nodeId: number | number[];          // Single node ID or array of node IDs that share this configuration
  availableStats: MythNodeStat[];     // All stats available in this node
  isDataComplete: boolean;            // Flag to track if all data has been collected
  lastUpdated?: string;               // ISO date string for tracking updates
  notes?: string;                     // Optional notes for data collection
}

// Helper function to create stat from stats-config with level data
const createMythNodeStat = (
  statKey: string,
  maxLevel: number,
  levels: StatLevelData[]
): MythNodeStat => {
  const statInfo = getStatInfo(statKey);
  if (!statInfo) {
    throw new Error(`Stat key '${statKey}' not found in stats-config`);
  }

  // Calculate min/max from levels
  const values = levels.map(l => l.value);
  const minValue = Math.min(...values);
  const maxValue = Math.max(...values);

  return {
    statKey,
    name: statInfo.name,
    category: statInfo.category,
    isPercentage: statInfo.isPercentage,
    description: statInfo.description,
    icon: statInfo.icon,
    maxLevel,
    levels: [...levels].sort((a, b) => a.level - b.level), // Ensure sorted by level
    minValue,
    maxValue
  };
};

// Node data for myth level system
// TODO: Complete data collection for all nodes
export const mythLevelNodeData: MythLevelNodeData[] = [
  {
    nodeId: [1,4,5],
    isDataComplete: false, // Mark as incomplete until real data is collected
    lastUpdated: new Date().toISOString(),
    notes: "Placeholder data - needs actual game data collection",
    availableStats: [
      createMythNodeStat("pveIgnoreAccuracy", 10, [
        { level: 1, value: 1, holyPower: 40 },
        { level: 2, value: 2, holyPower: 50 },
        { level: 3, value: 3, holyPower: 60 },
        { level: 4, value: 4, holyPower: 65 },
        { level: 5, value: 5, holyPower: 70 },
        { level: 6, value: 6, holyPower: 75 },
        { level: 7, value: 7, holyPower: 80 },
        { level: 8, value: 10, holyPower: 100 },
        { level: 9, value: 12, holyPower: 120 },
        { level: 10, value: 16, holyPower: 140 },
      ]),
      createMythNodeStat("pvpIgnoreAccuracy", 10, [
        { level: 1, value: 1, holyPower: 40 },
        { level: 2, value: 2, holyPower: 50 },
        { level: 3, value: 3, holyPower: 60 },
        { level: 4, value: 4, holyPower: 65 },
        { level: 5, value: 5, holyPower: 70 },
        { level: 6, value: 6, holyPower: 75 },
        { level: 7, value: 7, holyPower: 80 },
        { level: 8, value: 10, holyPower: 100 },
        { level: 9, value: 12, holyPower: 120 },
        { level: 10, value: 16, holyPower: 140 },
      ]),
      createMythNodeStat("pvpCancelIgnoreDamageReduce", 5, [
        { level: 1, value: 1, holyPower: 55 },
        { level: 2, value: 2, holyPower: 66 },
        { level: 3, value: 3, holyPower: 88 },
        { level: 4, value: 5, holyPower: 99 },
        { level: 5, value: 8, holyPower: 111 }
      ]),
      createMythNodeStat("pveCancelIgnoreDamageReduce", 5, [
        { level: 1, value: 1, holyPower: 55 },
        { level: 2, value: 2, holyPower: 66 },
        { level: 3, value: 3, holyPower: 88 },
        { level: 4, value: 5, holyPower: 99 },
        { level: 5, value: 8, holyPower: 111 }
      ]),
      createMythNodeStat("pvpDefense", 5, [
        { level: 1, value: 1, holyPower: 70 },
        { level: 2, value: 2, holyPower: 84 },
        { level: 3, value: 3, holyPower: 112 },
        { level: 4, value: 5, holyPower: 120 },
        { level: 5, value: 8, holyPower: 140 }
      ]),
      createMythNodeStat("pveDefense", 5, [
        { level: 1, value: 1, holyPower: 60 },
        { level: 2, value: 2, holyPower: 84 },
        { level: 3, value: 3, holyPower: 112 },
        { level: 4, value: 5, holyPower: 120 },
        { level: 5, value: 8, holyPower: 140 }
      ]),
      createMythNodeStat("hp", 5, [
        { level: 1, value: 5, holyPower: 60 },
        { level: 2, value: 10, holyPower: 72 },
        { level: 3, value: 15, holyPower: 90 },
        { level: 4, value: 20, holyPower: 100 },
        { level: 5, value: 30, holyPower: 110 }
      ])
    ]
  },
  {
    nodeId: [2,6, 7],
    isDataComplete: false,
    lastUpdated: new Date().toISOString(),
    notes: "Placeholder data - needs actual game data collection",
    availableStats: [
      createMythNodeStat("ignoreEvasion", 10, [
        { level: 1, value: 1, holyPower: 40 },
        { level: 2, value: 2, holyPower: 50 },
        { level: 3, value: 3, holyPower: 60 },
        { level: 4, value: 4, holyPower: 65 },
        { level: 5, value: 5, holyPower: 70 },
        { level: 6, value: 6, holyPower: 75 },
        { level: 7, value: 7, holyPower: 80 },
        { level: 8, value: 9, holyPower: 100 },
        { level: 9, value: 12, holyPower: 120 },
        { level: 10, value: 16, holyPower: 140 }
      ]),
      createMythNodeStat("cancelIgnoreEvasion", 10, [
        { level: 1, value: 1, holyPower: 40 },
        { level: 2, value: 2, holyPower: 50 },
        { level: 3, value: 6, holyPower: 60 },
        { level: 4, value: 4, holyPower: 65 },
        { level: 5, value: 5, holyPower: 70 },
        { level: 6, value: 6, holyPower: 75 },
        { level: 7, value: 7, holyPower: 80 },
        { level: 8, value: 9, holyPower: 100 },
        { level: 9, value: 12, holyPower: 120 },
        { level: 10, value: 16, holyPower: 140 }
      ]),
      createMythNodeStat("ignoreDamageReduce", 5, [
        { level: 1, value: 1, holyPower: 55 },
        { level: 2, value: 2, holyPower: 66 },
        { level: 3, value: 3, holyPower: 88 },
        { level: 4, value: 5, holyPower: 99 },
        { level: 5, value: 8, holyPower: 111 },
      ]),
      createMythNodeStat("cancelIgnoreDamageReduce", 5, [
        { level: 1, value: 1, holyPower: 55 },
        { level: 2, value: 2, holyPower: 66 },
        { level: 3, value: 3, holyPower: 88 },
        { level: 4, value: 5, holyPower: 99 },
        { level: 5, value: 8, holyPower: 111 },
      ]),
      createMythNodeStat("ignorePenetration", 5, [
        { level: 1, value: 3, holyPower: 70 },
        { level: 2, value: 4, holyPower: 84 },
        { level: 3, value: 5, holyPower: 112 },
        { level: 4, value: 7, holyPower: 168 },
        { level: 5, value: 10, holyPower: 140 }
      ]),
      createMythNodeStat("cancelIgnorePenetration", 5, [
        { level: 1, value: 3, holyPower: 70 },
        { level: 2, value: 4, holyPower: 84 },
        { level: 3, value: 5, holyPower: 112 },
        { level: 4, value: 8, holyPower: 120 },
        { level: 5, value: 10, holyPower: 140 }
      ])
    ]
  },
  {
    nodeId: [3, 8, 9], // Nodes 3 and 8 share the same configuration
    isDataComplete: false,
    lastUpdated: new Date().toISOString(),
    notes: "Updated with data from image - shared configuration for nodes 3 and 8",
    availableStats: [
      createMythNodeStat("pvpIgnoreEvasion", 10, [
        { level: 1, value: 1, holyPower: 40 },
        { level: 2, value: 2, holyPower: 50 },
        { level: 3, value: 3, holyPower: 60 },
        { level: 4, value: 4, holyPower: 65 },
        { level: 5, value: 5, holyPower: 70 },
        { level: 6, value: 6, holyPower: 75 },
        { level: 7, value: 7, holyPower: 80 },
        { level: 8, value: 9, holyPower: 100 },
        { level: 9, value: 12, holyPower: 120 },
        { level: 10, value: 16, holyPower: 200 }
      ]),
      createMythNodeStat("pveIgnoreEvasion", 10, [
        { level: 1, value: 1, holyPower: 40 },
        { level: 2, value: 2, holyPower: 50 },
        { level: 3, value: 3, holyPower: 60 },
        { level: 4, value: 4, holyPower: 65 },
        { level: 5, value: 5, holyPower: 70 },
        { level: 6, value: 6, holyPower: 75 },
        { level: 7, value: 7, holyPower: 80 },
        { level: 8, value: 9, holyPower: 100 },
        { level: 9, value: 12, holyPower: 120 },
        { level: 10, value: 16, holyPower: 200 }
      ]),
      createMythNodeStat("pvpIgnoreDamageReduce", 5, [
        { level: 1, value: 1, holyPower: 55 },
        { level: 2, value: 2, holyPower: 66 },
        { level: 3, value: 3, holyPower: 88 },
        { level: 4, value: 4, holyPower: 60 },
        { level: 5, value: 16, holyPower: 65 },
      ]),
      createMythNodeStat("pveIgnoreDamageReduce", 5, [
        { level: 1, value: 1, holyPower: 55 },
        { level: 2, value: 2, holyPower: 66 },
        { level: 3, value: 3, holyPower: 88 },
        { level: 4, value: 4, holyPower: 60 },
        { level: 5, value: 16, holyPower: 65 },
      ]),
      createMythNodeStat("pvpAllAttackUp", 5, [
        { level: 1, value: 1, holyPower: 60 },
        { level: 2, value: 2, holyPower: 80 },
        { level: 3, value: 3, holyPower: 96 },
        { level: 4, value: 4, holyPower: 100 },
        { level: 5, value: 8, holyPower: 120 },
      ]),
      createMythNodeStat("pveAllAttackUp", 5, [
        { level: 1, value: 1, holyPower: 60 },
        { level: 2, value: 2, holyPower: 80 },
        { level: 3, value: 3, holyPower: 96 },
        { level: 4, value: 4, holyPower: 100 },
        { level: 5, value: 8, holyPower: 120 },
      ]),
      createMythNodeStat("pvpCancelIgnorePenetration", 5, [
        { level: 1, value: 3, holyPower: 70 },
        { level: 2, value: 4, holyPower: 80 },
        { level: 3, value: 5, holyPower: 90 },
        { level: 4, value: 6, holyPower: 100 },
        { level: 5, value: 10, holyPower: 120 }
      ]),
      createMythNodeStat("pveCancelIgnorePenetration", 5, [
        { level: 1, value: 3, holyPower: 70 },
        { level: 2, value: 4, holyPower: 80 },
        { level: 3, value: 5, holyPower: 90 },
        { level: 4, value: 6, holyPower: 100 },
        { level: 5, value: 10, holyPower: 120 }
      ])
    ]
  },
  {
    nodeId: 10, // Nodes 3 and 8 share the same configuration
    isDataComplete: false,
    lastUpdated: new Date().toISOString(),
    notes: "Updated with data from image - shared configuration for nodes 3 and 8",
    availableStats: [
      createMythNodeStat("pveIgnoreAccuracy", 10, [
        { level: 1, value: 2, holyPower: 40 },
        { level: 2, value: 3, holyPower: 50 },
        { level: 3, value: 4, holyPower: 60 },
        { level: 4, value: 5, holyPower: 78 },
        { level: 5, value: 6, holyPower: 84 },
        { level: 6, value: 7, holyPower: 90 },
        { level: 7, value: 8, holyPower: 96 },
        { level: 8, value: 11, holyPower: 120 },
        { level: 9, value: 12, holyPower: 140 },
        { level: 10, value: 21, holyPower: 160 },
      ]),
      createMythNodeStat("pvpIgnoreAccuracy", 10, [
        { level: 1, value: 2, holyPower: 40 },
        { level: 2, value: 3, holyPower: 50 },
        { level: 3, value: 4, holyPower: 60 },
        { level: 4, value: 5, holyPower: 78 },
        { level: 5, value: 6, holyPower: 84 },
        { level: 6, value: 7, holyPower: 90 },
        { level: 7, value: 8, holyPower: 96 },
        { level: 8, value: 11, holyPower: 120 },
        { level: 9, value: 12, holyPower: 140 },
        { level: 10, value: 21, holyPower: 160 },
      ]),
      createMythNodeStat("pvpCancelIgnoreDamageReduce", 5, [
        { level: 1, value: 4, holyPower: 70 },
        { level: 2, value: 5, holyPower: 84 },
        { level: 3, value: 6, holyPower: 112 },
        { level: 4, value: 7, holyPower: 168 },
        { level: 5, value: 11, holyPower: 280 }
      ]),
      createMythNodeStat("pveCancelIgnoreDamageReduce", 5, [
        { level: 1, value: 4, holyPower: 70 },
        { level: 2, value: 5, holyPower: 84 },
        { level: 3, value: 6, holyPower: 112 },
        { level: 4, value: 7, holyPower: 168 },
        { level: 5, value: 11, holyPower: 280 }
      ]),
      createMythNodeStat("pvpDefense", 5, [
        { level: 1, value: 2, holyPower: 115 },
        { level: 2, value: 3, holyPower: 138 },
        { level: 3, value: 3, holyPower: 112 },
        { level: 4, value: 5, holyPower: 120 },
        { level: 5, value: 11, holyPower: 140 }
      ]),
      createMythNodeStat("pveDefense", 5, [
        { level: 1, value: 2, holyPower: 115 },
        { level: 2, value: 3, holyPower: 138 },
        { level: 3, value: 3, holyPower: 112 },
        { level: 4, value: 5, holyPower: 120 },
        { level: 5, value: 11, holyPower: 140 }
      ]),
      createMythNodeStat("hp", 5, [
        { level: 1, value: 25, holyPower: 100 },
        { level: 2, value: 30, holyPower: 120 },
        { level: 3, value: 35, holyPower: 90 },
        { level: 4, value: 40, holyPower: 100 },
        { level: 5, value: 45, holyPower: 110 }
      ])
    ]
  },
  {
    nodeId: 11,
    isDataComplete: false,
    lastUpdated: new Date().toISOString(),
    notes: "Updated with data from image - shared configuration for nodes 3 and 8",
    availableStats: [
      createMythNodeStat("pvpDamageReduce", 5, [
        { level: 1, value: 2, holyPower: 60 },
        { level: 2, value: 2, holyPower: 72 },
        { level: 3, value: 6, holyPower: 96 },
        { level: 4, value: 8, holyPower: 120 },
        { level: 5, value: 14, holyPower: 200 }
      ]),
      createMythNodeStat("pveDamageReduce", 5, [
        { level: 1, value: 2, holyPower: 60 },
        { level: 2, value: 2, holyPower: 72 },
        { level: 3, value: 6, holyPower: 96 },
        { level: 4, value: 8, holyPower: 120 },
        { level: 5, value: 14, holyPower: 200 }
      ])
    ]
  },
  {
    nodeId: [12, 14],
    isDataComplete: false,
    lastUpdated: new Date().toISOString(),
    notes: "Placeholder data - needs actual game data collection",
    availableStats: [
      createMythNodeStat("ignoreEvasion", 10, [
        { level: 1, value: 2, holyPower: 40 },
        { level: 2, value: 2, holyPower: 50 },
        { level: 3, value: 4, holyPower: 72 },
        { level: 4, value: 4, holyPower: 78 },
        { level: 5, value: 6, holyPower: 84 },
        { level: 6, value: 7, holyPower: 90 },
        { level: 7, value: 8, holyPower: 96 },
        { level: 8, value: 9, holyPower: 102 },
        { level: 9, value: 12, holyPower: 120 },
        { level: 10, value: 21, holyPower: 140 }
      ]),
      createMythNodeStat("cancelIgnoreEvasion", 10, [
        { level: 1, value: 2, holyPower: 40 },
        { level: 2, value: 2, holyPower: 50 },
        { level: 3, value: 4, holyPower: 72 },
        { level: 4, value: 4, holyPower: 78 },
        { level: 5, value: 6, holyPower: 84 },
        { level: 6, value: 7, holyPower: 90 },
        { level: 7, value: 8, holyPower: 96 },
        { level: 8, value: 9, holyPower: 102 },
        { level: 9, value: 12, holyPower: 120 },
        { level: 10, value: 21, holyPower: 140 }
      ]),
      createMythNodeStat("ignoreDamageReduce", 5, [
        { level: 1, value: 4, holyPower: 70 },
        { level: 2, value: 5, holyPower: 84 },
        { level: 3, value: 6, holyPower: 112 },
        { level: 4, value: 8, holyPower: 168 },
        { level: 5, value: 11, holyPower: 200 },
      ]),
      createMythNodeStat("cancelIgnoreDamageReduce", 5, [
        { level: 1, value: 4, holyPower: 70 },
        { level: 2, value: 5, holyPower: 84 },
        { level: 3, value: 6, holyPower: 112 },
        { level: 4, value: 8, holyPower: 168 },
        { level: 5, value: 11, holyPower: 200 },
      ]),
      createMythNodeStat("ignorePenetration", 5, [
        { level: 1, value: 5, holyPower: 115 },
        { level: 2, value: 6, holyPower: 138 },
        { level: 3, value: 7, holyPower: 112 },
        { level: 4, value: 8, holyPower: 168 },
        { level: 5, value: 13, holyPower: 140 }
      ]),
      createMythNodeStat("cancelIgnorePenetration", 5, [
        { level: 1, value: 5, holyPower: 115 },
        { level: 2, value: 6, holyPower: 138 },
        { level: 3, value: 7, holyPower: 112 },
        { level: 4, value: 8, holyPower: 168 },
        { level: 5, value: 13, holyPower: 140 }
      ]),
    ]
  },
  {
    nodeId: 13,
    isDataComplete: true,
    lastUpdated: new Date().toISOString(),
    notes: "Updated with data from image - shared configuration for nodes 3 and 8",
    availableStats: [
      createMythNodeStat("pveNormalDamageUp", 10, [
        { level: 1, value: 1, holyPower: 60 },
        { level: 2, value: 2, holyPower: 60 },
        { level: 3, value: 3, holyPower: 72 },
        { level: 4, value: 4, holyPower: 78 },
        { level: 5, value: 5, holyPower: 84 },
        { level: 6, value: 6, holyPower: 90 },
        { level: 7, value: 7, holyPower: 100 },
        { level: 8, value: 8, holyPower: 120 },
        { level: 9, value: 9, holyPower: 200 },
        { level: 10, value: 13, holyPower: 200 },
      ]),
      createMythNodeStat("pvpNormalDamageUp", 5, [
        { level: 1, value: 1, holyPower: 60 },
        { level: 2, value: 2, holyPower: 60 },
        { level: 3, value: 3, holyPower: 72 },
        { level: 4, value: 4, holyPower: 78 },
        { level: 5, value: 5, holyPower: 84 },
        { level: 6, value: 6, holyPower: 90 },
        { level: 7, value: 7, holyPower: 100 },
        { level: 8, value: 8, holyPower: 120 },
        { level: 9, value: 9, holyPower: 200 },
        { level: 10, value: 13, holyPower: 200 },
      ]),
    ]
  },
  {
    nodeId: 15,
    isDataComplete: false,
    lastUpdated: new Date().toISOString(),
    notes: "Updated with data from image - shared configuration for nodes 3 and 8",
    availableStats: [
      createMythNodeStat("pvpIgnoreDamageReduce", 5, [
        { level: 1, value: 2, holyPower: 55 },
        { level: 2, value: 3, holyPower: 66 },
        { level: 3, value: 6, holyPower: 88 },
        { level: 4, value: 10, holyPower: 99 },
        { level: 5, value: 16, holyPower: 111 }
      ]),
      createMythNodeStat("pveIgnoreDamageReduce", 5, [
        { level: 1, value: 2, holyPower: 55 },
        { level: 2, value: 3, holyPower: 66 },
        { level: 3, value: 6, holyPower: 88 },
        { level: 4, value: 10, holyPower: 99 },
        { level: 5, value: 16, holyPower: 111 }
      ]),
    ]
  },
  {
    nodeId: 16, // Nodes 3 and 8 share the same configuration
    isDataComplete: false,
    lastUpdated: new Date().toISOString(),
    notes: "Updated with data from image - shared configuration for nodes 3 and 8",
    availableStats: [
      createMythNodeStat("pvpIgnoreEvasion", 10, [
        { level: 1, value: 2, holyPower: 48 },
        { level: 2, value: 3, holyPower: 56 },
        { level: 3, value: 4, holyPower: 72 },
        { level: 4, value: 5, holyPower: 78 },
        { level: 5, value: 6, holyPower: 84 },
        { level: 6, value: 7, holyPower: 90 },
        { level: 7, value: 8, holyPower: 96 },
        { level: 8, value: 11, holyPower: 120 },
        { level: 9, value: 15, holyPower: 144 },
        { level: 10, value: 21, holyPower: 166 }
      ]),
      createMythNodeStat("pveIgnoreEvasion", 10, [
        { level: 1, value: 2, holyPower: 48 },
        { level: 2, value: 3, holyPower: 56 },
        { level: 3, value: 4, holyPower: 72 },
        { level: 4, value: 5, holyPower: 78 },
        { level: 5, value: 6, holyPower: 84 },
        { level: 6, value: 7, holyPower: 90 },
        { level: 7, value: 8, holyPower: 96 },
        { level: 8, value: 11, holyPower: 120 },
        { level: 9, value: 15, holyPower: 144 },
        { level: 10, value: 21, holyPower: 166 }
      ]),
      createMythNodeStat("pvpIgnoreDamageReduce", 5, [
        { level: 1, value: 4, holyPower: 70 },
        { level: 2, value: 5, holyPower: 84 },
        { level: 3, value: 6, holyPower: 112 },
        { level: 4, value: 8, holyPower: 168 },
        { level: 5, value: 16, holyPower: 200 },
      ]),
      createMythNodeStat("pveIgnoreDamageReduce", 5, [
        { level: 1, value: 4, holyPower: 70 },
        { level: 2, value: 5, holyPower: 84 },
        { level: 3, value: 6, holyPower: 112 },
        { level: 4, value: 8, holyPower: 168 },
        { level: 5, value: 16, holyPower: 200 },
      ]),
      createMythNodeStat("pvpAllAttackUp", 5, [
        { level: 1, value: 4, holyPower: 100 },
        { level: 2, value: 5, holyPower: 120 },
        { level: 3, value: 6, holyPower: 140 },
        { level: 4, value: 7, holyPower: 160 },
        { level: 5, value: 11, holyPower: 200 },
      ]),
      createMythNodeStat("pveAllAttackUp", 5, [
        { level: 1, value: 4, holyPower: 100 },
        { level: 2, value: 5, holyPower: 120 },
        { level: 3, value: 6, holyPower: 140 },
        { level: 4, value: 7, holyPower: 160 },
        { level: 5, value: 11, holyPower: 200 },
      ]),
      createMythNodeStat("pvpCancelIgnorePenetration", 5, [
        { level: 1, value: 4, holyPower: 115 },
        { level: 2, value: 5, holyPower: 138 },
        { level: 3, value: 6, holyPower: 150 },
        { level: 4, value: 7, holyPower: 160 },
        { level: 5, value: 13, holyPower: 200 }
      ]),
      createMythNodeStat("pveCancelIgnorePenetration", 5, [
        { level: 1, value: 4, holyPower: 115 },
        { level: 2, value: 5, holyPower: 138 },
        { level: 3, value: 6, holyPower: 150 },
        { level: 4, value: 7, holyPower: 160 },
        { level: 5, value: 13, holyPower: 200 }
      ]),
    ]
  },
  {
    nodeId: [17, 18],
    isDataComplete: false,
    lastUpdated: new Date().toISOString(),
    notes: "Updated with data from image",
    availableStats: [
      createMythNodeStat("pveDefenseRate", 10, [
        { level: 1, value: 4, holyPower: 60 },
        { level: 2, value: 8, holyPower: 70 },
        { level: 3, value: 12, holyPower: 80 },
        { level: 4, value: 16, holyPower: 85 },
        { level: 5, value: 20, holyPower: 90 },
        { level: 6, value: 24, holyPower: 95 },
        { level: 7, value: 28, holyPower: 100 },
        { level: 8, value: 32, holyPower: 120 },
        { level: 9, value: 40, holyPower: 140 },
        { level: 10, value: 64, holyPower: 160 }
      ]),
      createMythNodeStat("pvpDefenseRate", 10, [
        { level: 1, value: 4, holyPower: 60 },
        { level: 2, value: 8, holyPower: 70 },
        { level: 3, value: 12, holyPower: 80 },
        { level: 4, value: 16, holyPower: 85 },
        { level: 5, value: 20, holyPower: 90 },
        { level: 6, value: 24, holyPower: 95 },
        { level: 7, value: 28, holyPower: 100 },
        { level: 8, value: 32, holyPower: 120 },
        { level: 9, value: 40, holyPower: 140 },
        { level: 10, value: 64, holyPower: 160 }
      ]),
      createMythNodeStat("resistCritDmg", 2, [
        { level: 1, value: 1, holyPower: 152 },
        { level: 2, value: 2, holyPower: 200 },
      ]),
      createMythNodeStat("resistSkillAmp", 2, [
        { level: 1, value: 1, holyPower: 164 },
        { level: 2, value: 2, holyPower: 200 }
      ]),
      createMythNodeStat("hp", 5, [
        { level: 1, value: 10, holyPower: 90 },
        { level: 2, value: 15, holyPower: 104 },
        { level: 3, value: 20, holyPower: 120 },
        { level: 4, value: 25, holyPower: 140 },
        { level: 5, value: 35, holyPower: 200 }
      ])
    ]
  },
  {
    nodeId: [19,20,21,22],
    isDataComplete: false,
    lastUpdated: new Date().toISOString(),
    notes: "Updated with data from image",
    availableStats: [
      createMythNodeStat("attackRate", 10, [
        { level: 1, value: 4, holyPower: 60 },
        { level: 2, value: 8, holyPower: 70 },
        { level: 3, value: 12, holyPower: 80 },
        { level: 4, value: 16, holyPower: 85 },
        { level: 5, value: 20, holyPower: 90 },
        { level: 6, value: 24, holyPower: 95 },
        { level: 7, value: 28, holyPower: 100 },
        { level: 8, value: 36, holyPower: 120 },
        { level: 9, value: 40, holyPower: 80 },
        { level: 10, value: 64, holyPower: 90 }
      ]),
      createMythNodeStat("defenseRate", 2, [
        { level: 1, value: 4, holyPower: 60 },
        { level: 2, value: 8, holyPower: 70 },
        { level: 3, value: 12, holyPower: 80 },
        { level: 4, value: 16, holyPower: 85 },
        { level: 5, value: 20, holyPower: 90 },
        { level: 6, value: 24, holyPower: 95 },
        { level: 7, value: 28, holyPower: 100 },
        { level: 8, value: 36, holyPower: 120 },
        { level: 9, value: 40, holyPower: 80 },
        { level: 10, value: 64, holyPower: 90 }
      ]),
      createMythNodeStat("resistCritDmg", 3, [
        { level: 1, value: 1, holyPower: 132 },
        { level: 2, value: 2, holyPower: 160 },
        { level: 3, value: 3, holyPower: 200 }
      ]),
      createMythNodeStat("addDamage", 5, [
        { level: 1, value: 2, holyPower: 80 },
        { level: 2, value: 3, holyPower: 90 },
        { level: 3, value: 4, holyPower: 116 },
        { level: 4, value: 5, holyPower: 160 },
        { level: 5, value: 9, holyPower: 260 },
      ]),
      createMythNodeStat("hp", 5, [
        { level: 1, value: 10, holyPower: 80 },
        { level: 2, value: 15, holyPower: 92 },
        { level: 3, value: 20, holyPower: 100 },
        { level: 4, value: 25, holyPower: 120 },
        { level: 5, value: 35, holyPower: 140 },
      ]),
      createMythNodeStat("ignoreResistCritDmg", 3, [
        { level: 1, value: 1, holyPower: 132 },
        { level: 2, value: 2, holyPower: 160 },
        { level: 3, value: 3, holyPower: 200 }
      ])
    ]
  },
  {
    nodeId: [23,24],
    isDataComplete: false,
    lastUpdated: new Date().toISOString(),
    notes: "Updated with additional level data from user input",
    availableStats: [
      createMythNodeStat("pvpAttackRate", 10, [
        { level: 1, value: 4, holyPower: 60 },
        { level: 2, value: 64, holyPower: 70 },
        { level: 3, value: 12, holyPower: 80 },
        { level: 4, value: 16, holyPower: 85 },
        { level: 5, value: 20, holyPower: 90 },
        { level: 6, value: 24, holyPower: 95 },
        { level: 7, value: 28, holyPower: 100 },
        { level: 8, value: 36, holyPower: 120 },
        { level: 9, value: 40, holyPower: 140 },
        { level: 10, value: 64, holyPower: 220 }
      ]),
      createMythNodeStat("pveAttackRate", 10, [
        { level: 1, value: 4, holyPower: 60 },
        { level: 2, value: 8, holyPower: 70 },
        { level: 3, value: 12, holyPower: 80 },
        { level: 4, value: 16, holyPower: 85 },
        { level: 5, value: 20, holyPower: 90 },
        { level: 6, value: 24, holyPower: 95 },
        { level: 7, value: 28, holyPower: 100 },
        { level: 8, value: 36, holyPower: 120 },
        { level: 9, value: 40, holyPower: 140 },
        { level: 10, value: 64, holyPower: 220 }
      ]),
      createMythNodeStat("pvpAddDamage", 5, [
        { level: 1, value: 2, holyPower: 75 },
        { level: 2, value: 3, holyPower: 86 },
        { level: 3, value: 4, holyPower: 108 },
        { level: 4, value: 5, holyPower: 160 },
        { level: 5, value: 9, holyPower: 240 }
      ]),
      createMythNodeStat("pveAddDamage", 5, [
        { level: 1, value: 2, holyPower: 75 },
        { level: 2, value: 3, holyPower: 86 },
        { level: 3, value: 4, holyPower: 108 },
        { level: 4, value: 5, holyPower: 160 },
        { level: 5, value: 9, holyPower: 240 }
      ]),
      createMythNodeStat("pvpAllAttackUp", 5, [
        { level: 1, value: 2, holyPower: 80 },
        { level: 2, value: 3, holyPower: 100 },
        { level: 3, value: 4, holyPower: 116 },
        { level: 4, value: 5, holyPower: 160 },
        { level: 5, value: 9, holyPower: 260 }
      ]),
      createMythNodeStat("pveAllAttackUp", 5, [
        { level: 1, value: 2, holyPower: 80 },
        { level: 2, value: 3, holyPower: 100 },
        { level: 3, value: 4, holyPower: 116 },
        { level: 4, value: 5, holyPower: 160 },
        { level: 5, value: 9, holyPower: 260 }
      ]),
      createMythNodeStat("pvpIgnoreResistCritDmg", 2, [
        { level: 1, value: 1, holyPower: 8 },
        { level: 2, value: 3, holyPower: 16 }
      ]),
      createMythNodeStat("pveIgnoreResistCritDmg", 2, [
         { level: 1, value: 1, holyPower: 8 },
         { level: 2, value: 3, holyPower: 16 }
       ])
     ]
   },
   {
     nodeId: 25,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with additional level data from user input",
     availableStats: [
       createMythNodeStat("pvpDefenseRate", 10, [
         { level: 1, value: 8, holyPower: 15 }, // Original data
         { level: 2, value: 12, holyPower: 80 },
         { level: 3, value: 16, holyPower: 92 },
         { level: 4, value: 20, holyPower: 98 },
         { level: 5, value: 24, holyPower: 104 },
         { level: 6, value: 28, holyPower: 110 },
         { level: 7, value: 32, holyPower: 116 },
         { level: 8, value: 44, holyPower: 140 },
         { level: 9, value: 60, holyPower: 160 }, // Inferred progression
         { level: 10, value: 84, holyPower: 200 } // Inferred progression
       ]),
       createMythNodeStat("pveDefenseRate", 10, [
         { level: 1, value: 8, holyPower: 15 }, // Original data
         { level: 2, value: 12, holyPower: 80 },
         { level: 3, value: 16, holyPower: 92 },
         { level: 4, value: 20, holyPower: 98 },
         { level: 5, value: 24, holyPower: 104 },
         { level: 6, value: 28, holyPower: 110 },
         { level: 7, value: 32, holyPower: 116 },
         { level: 8, value: 44, holyPower: 140 },
         { level: 9, value: 60, holyPower: 160 }, // Inferred progression
         { level: 10, value: 84, holyPower: 200 } // Inferred progression
       ]),
       createMythNodeStat("hp", 5, [
         { level: 1, value: 25, holyPower: 140 }, // Inferred from pattern
         { level: 2, value: 30, holyPower: 158 },
         { level: 3, value: 35, holyPower: 180 }, // Inferred progression
         { level: 4, value: 40, holyPower: 200 }, // Inferred progression
         { level: 5, value: 50, holyPower: 240 } // Inferred progression
       ]),
       createMythNodeStat("resistCritDmg", 2, [
         { level: 1, value: 2, holyPower: 188 },
         { level: 2, value: 3, holyPower: 220 } // Inferred progression
       ]),
       createMythNodeStat("resistSkillAmp", 2, [
         { level: 1, value: 2, holyPower: 260 },
         { level: 2, value: 3, holyPower: 300 } // Inferred progression
       ])
     ]
   },
   {
     nodeId: 26,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with additional level data from user input",
     availableStats: [
       createMythNodeStat("pvpDefenseRate", 5, [
         { level: 1, value: 10, holyPower: 80 },
         { level: 2, value: 20, holyPower: 92 },
         { level: 3, value: 35, holyPower: 116 },
         { level: 4, value: 60, holyPower: 150 }, // Inferred progression
         { level: 5, value: 100, holyPower: 200 } // Inferred progression
       ]),
       createMythNodeStat("pveDefenseRate", 5, [
         { level: 1, value: 10, holyPower: 80 },
         { level: 2, value: 20, holyPower: 92 },
         { level: 3, value: 35, holyPower: 116 },
         { level: 4, value: 60, holyPower: 150 }, // Inferred progression
         { level: 5, value: 100, holyPower: 200 } // Inferred progression
       ])
     ]
   },
   {
     nodeId: [27,29],
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with complete level progression data",
     availableStats: [
       createMythNodeStat("pvpAttackRate", 10, [
         { level: 1, value: 8, holyPower: 68 },
         { level: 2, value: 12, holyPower: 80 }, // Inferred progression
         { level: 3, value: 16, holyPower: 92 },
         { level: 4, value: 20, holyPower: 98 }, // Inferred progression
         { level: 5, value: 24, holyPower: 104 }, // Inferred progression
         { level: 6, value: 28, holyPower: 110 },
         { level: 7, value: 32, holyPower: 116 }, // Inferred progression
         { level: 8, value: 44, holyPower: 140 },
         { level: 9, value: 60, holyPower: 164 }, // Inferred progression
         { level: 10, value: 84, holyPower: 188 } // Inferred progression
       ]),
       createMythNodeStat("pveAttackRate", 10, [
         { level: 1, value: 8, holyPower: 68 },
         { level: 2, value: 12, holyPower: 80 }, // Inferred progression
         { level: 3, value: 16, holyPower: 92 },
         { level: 4, value: 20, holyPower: 98 }, // Inferred progression
         { level: 5, value: 24, holyPower: 104 }, // Inferred progression
         { level: 6, value: 28, holyPower: 110 },
         { level: 7, value: 32, holyPower: 116 }, // Inferred progression
         { level: 8, value: 44, holyPower: 140 },
         { level: 9, value: 60, holyPower: 164 }, // Inferred progression
         { level: 10, value: 84, holyPower: 188 } // Inferred progression
       ]),
       createMythNodeStat("pvpDefenseRate", 10, [
         { level: 1, value: 8, holyPower: 68 }, // Inferred - same as attack rate
         { level: 2, value: 12, holyPower: 80 },
         { level: 3, value: 16, holyPower: 92 },
         { level: 4, value: 20, holyPower: 98 },
         { level: 5, value: 24, holyPower: 104 }, // Inferred progression
         { level: 6, value: 28, holyPower: 110 }, // Inferred progression
         { level: 7, value: 32, holyPower: 116 }, // Inferred progression
         { level: 8, value: 44, holyPower: 140 }, // Inferred progression
         { level: 9, value: 60, holyPower: 164 },
         { level: 10, value: 84, holyPower: 188 } // Inferred progression
       ]),
       createMythNodeStat("pveDefenseRate", 10, [
         { level: 1, value: 8, holyPower: 68 }, // Inferred - same as attack rate
         { level: 2, value: 12, holyPower: 80 },
         { level: 3, value: 16, holyPower: 92 },
         { level: 4, value: 20, holyPower: 98 },
         { level: 5, value: 24, holyPower: 104 }, // Inferred progression
         { level: 6, value: 28, holyPower: 110 }, // Inferred progression
         { level: 7, value: 32, holyPower: 116 }, // Inferred progression
         { level: 8, value: 44, holyPower: 140 }, // Inferred progression
         { level: 9, value: 60, holyPower: 164 },
         { level: 10, value: 84, holyPower: 188 } // Inferred progression
       ]),
       createMythNodeStat("hp", 5, [
         { level: 1, value: 25, holyPower: 120 },
         { level: 2, value: 30, holyPower: 150 }, // Inferred progression
         { level: 3, value: 35, holyPower: 180 },
         { level: 4, value: 40, holyPower: 260 },
         { level: 5, value: 50, holyPower: 300 } // Inferred progression
       ]),
       createMythNodeStat("pvpAddDamage", 5, [
         { level: 1, value: 3, holyPower: 120 },
         { level: 2, value: 4, holyPower: 150 }, // Inferred progression
         { level: 3, value: 6, holyPower: 180 },
         { level: 4, value: 8, holyPower: 210 }, // Inferred progression
         { level: 5, value: 12, holyPower: 240 } // Inferred progression
       ]),
       createMythNodeStat("pveAddDamage", 5, [
         { level: 1, value: 3, holyPower: 120 },
         { level: 2, value: 4, holyPower: 150 }, // Inferred progression
         { level: 3, value: 6, holyPower: 180 },
         { level: 4, value: 8, holyPower: 210 }, // Inferred progression
         { level: 5, value: 12, holyPower: 240 } // Inferred progression
       ]),
       createMythNodeStat("pvpIgnoreResistCritDmg", 3, [
         { level: 1, value: 2, holyPower: 204 },
         { level: 2, value: 3, holyPower: 230 }, // Inferred progression
         { level: 3, value: 4, holyPower: 256 } // Inferred progression
       ]),
       createMythNodeStat("pveIgnoreResistCritDmg", 3, [
         { level: 1, value: 2, holyPower: 204 },
         { level: 2, value: 3, holyPower: 230 }, // Inferred progression
         { level: 3, value: 4, holyPower: 256 } // Inferred progression
       ])
     ]
   },
   {
     nodeId: 28,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with complete level progression data",
     availableStats: [
       createMythNodeStat("pvpAllAttackUp", 10, [
         { level: 1, value: 2, holyPower: 76 },
         { level: 2, value: 3, holyPower: 90 },
         { level: 3, value: 4, holyPower: 104 },
         { level: 4, value: 5, holyPower: 111 },
         { level: 5, value: 6, holyPower: 118 },
         { level: 6, value: 7, holyPower: 125 }, // Inferred progression
         { level: 7, value: 8, holyPower: 132 }, // Inferred progression
         { level: 8, value: 10, holyPower: 145 }, // Inferred progression
         { level: 9, value: 12, holyPower: 160 }, // Inferred progression
         { level: 10, value: 18, holyPower: 180 } // Inferred progression
       ]),
       createMythNodeStat("pveAllAttackUp", 10, [
         { level: 1, value: 2, holyPower: 76 },
         { level: 2, value: 3, holyPower: 90 },
         { level: 3, value: 4, holyPower: 104 },
         { level: 4, value: 5, holyPower: 111 },
         { level: 5, value: 6, holyPower: 118 },
         { level: 6, value: 7, holyPower: 125 }, // Inferred progression
         { level: 7, value: 8, holyPower: 132 }, // Inferred progression
         { level: 8, value: 10, holyPower: 145 }, // Inferred progression
         { level: 9, value: 12, holyPower: 160 }, // Inferred progression
         { level: 10, value: 18, holyPower: 180 } // Inferred progression
       ])
     ]
   },
   {
     nodeId: 30,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with complete level progression data",
     availableStats: [
       createMythNodeStat("pvpAttackRate", 5, [
         { level: 1, value: 10, holyPower: 75 },
         { level: 2, value: 20, holyPower: 86 },
         { level: 3, value: 35, holyPower: 108 },
         { level: 4, value: 60, holyPower: 140 }, // Inferred progression
         { level: 5, value: 100, holyPower: 180 } // Inferred progression
       ]),
       createMythNodeStat("pveAttackRate", 5, [
         { level: 1, value: 10, holyPower: 75 },
         { level: 2, value: 20, holyPower: 86 },
         { level: 3, value: 35, holyPower: 108 },
         { level: 4, value: 60, holyPower: 140 }, // Inferred progression
         { level: 5, value: 100, holyPower: 180 } // Inferred progression
       ])
     ]
   },
   {
     nodeId: 31,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with complete level progression data",
     availableStats: [
       createMythNodeStat("pvpAttackRate", 10, [
         { level: 1, value: 8, holyPower: 68 },
         { level: 2, value: 12, holyPower: 80 }, // Inferred progression
         { level: 3, value: 16, holyPower: 92 },
         { level: 4, value: 20, holyPower: 98 }, // Inferred progression
         { level: 5, value: 24, holyPower: 104 },
         { level: 6, value: 28, holyPower: 110 },
         { level: 7, value: 32, holyPower: 116 }, // Inferred progression
         { level: 8, value: 40, holyPower: 130 }, // Inferred progression
         { level: 9, value: 60, holyPower: 150 }, // Inferred progression
         { level: 10, value: 84, holyPower: 180 } // Inferred progression
       ]),
       createMythNodeStat("pveAttackRate", 10, [
         { level: 1, value: 8, holyPower: 68 },
         { level: 2, value: 12, holyPower: 80 }, // Inferred progression
         { level: 3, value: 16, holyPower: 92 },
         { level: 4, value: 20, holyPower: 98 }, // Inferred progression
         { level: 5, value: 24, holyPower: 104 },
         { level: 6, value: 28, holyPower: 110 },
         { level: 7, value: 32, holyPower: 116 }, // Inferred progression
         { level: 8, value: 40, holyPower: 130 }, // Inferred progression
         { level: 9, value: 60, holyPower: 150 }, // Inferred progression
         { level: 10, value: 84, holyPower: 180 } // Inferred progression
       ]),
       createMythNodeStat("pvpAddDamage", 5, [
         { level: 1, value: 3, holyPower: 80 }, // Inferred holy power
         { level: 2, value: 4, holyPower: 104 },
         { level: 3, value: 6, holyPower: 132 },
         { level: 4, value: 9, holyPower: 200 }, // Inferred progression
         { level: 5, value: 12, holyPower: 300 }
       ]),
       createMythNodeStat("pveAddDamage", 5, [
         { level: 1, value: 3, holyPower: 80 }, // Inferred holy power
         { level: 2, value: 4, holyPower: 104 },
         { level: 3, value: 6, holyPower: 132 },
         { level: 4, value: 9, holyPower: 200 }, // Inferred progression
         { level: 5, value: 12, holyPower: 300 }
       ]),
       createMythNodeStat("pvpAllAttackUp", 5, [
         { level: 1, value: 3, holyPower: 120 },
         { level: 2, value: 5, holyPower: 150 }, // Inferred progression
         { level: 3, value: 7, holyPower: 180 }, // Inferred progression
         { level: 4, value: 9, holyPower: 210 }, // Inferred progression
         { level: 5, value: 12, holyPower: 240 } // Inferred progression
       ]),
       createMythNodeStat("pveAllAttackUp", 5, [
         { level: 1, value: 3, holyPower: 120 },
         { level: 2, value: 5, holyPower: 150 }, // Inferred progression
         { level: 3, value: 7, holyPower: 180 }, // Inferred progression
         { level: 4, value: 9, holyPower: 210 }, // Inferred progression
         { level: 5, value: 12, holyPower: 240 } // Inferred progression
       ]),
       createMythNodeStat("pvpIgnoreResistCritDmg", 3, [
         { level: 1, value: 2, holyPower: 204 },
         { level: 2, value: 3, holyPower: 230 }, // Inferred progression
         { level: 3, value: 4, holyPower: 256 } // Inferred progression
       ]),
       createMythNodeStat("pveIgnoreResistCritDmg", 3, [
         { level: 1, value: 2, holyPower: 204 },
         { level: 2, value: 3, holyPower: 230 }, // Inferred progression
         { level: 3, value: 4, holyPower: 256 } // Inferred progression
       ])
     ]
   },
   {
     nodeId: [32,33],
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with complete level progression data",
     availableStats: [
       createMythNodeStat("pveIgnoreAccuracy", 10, [
         { level: 1, value: 3, holyPower: 90 }, // Inferred holy power
         { level: 2, value: 4, holyPower: 95 }, // Inferred progression
         { level: 3, value: 5, holyPower: 100 },
         { level: 4, value: 6, holyPower: 105 },
         { level: 5, value: 7, holyPower: 110 },
         { level: 6, value: 8, holyPower: 115 },
         { level: 7, value: 10, holyPower: 130 }, // Inferred progression
         { level: 8, value: 12, holyPower: 145 }, // Inferred progression
         { level: 9, value: 14, holyPower: 160 },
         { level: 10, value: 18, holyPower: 240 }
       ]),
       createMythNodeStat("pvpIgnoreAccuracy", 10, [
         { level: 1, value: 3, holyPower: 90 }, // Inferred holy power
         { level: 2, value: 4, holyPower: 95 }, // Inferred progression
         { level: 3, value: 5, holyPower: 100 },
         { level: 4, value: 6, holyPower: 105 },
         { level: 5, value: 7, holyPower: 110 },
         { level: 6, value: 8, holyPower: 115 },
         { level: 7, value: 10, holyPower: 130 }, // Inferred progression
         { level: 8, value: 12, holyPower: 145 }, // Inferred progression
         { level: 9, value: 14, holyPower: 160 },
         { level: 10, value: 18, holyPower: 240 }
       ]),
       createMythNodeStat("resistCritDmg", 2, [
         { level: 1, value: 2, holyPower: 172 },
         { level: 2, value: 4, holyPower: 200 } // Inferred progression
       ]),
       createMythNodeStat("pvpResistSkillAmp", 2, [
         { level: 1, value: 2, holyPower: 184 },
         { level: 2, value: 4, holyPower: 220 } // Inferred progression
       ]),
       createMythNodeStat("pveResistSkillAmp", 2, [
         { level: 1, value: 2, holyPower: 184 },
         { level: 2, value: 4, holyPower: 220 } // Inferred progression
       ]),
       createMythNodeStat("hp", 3, [
         { level: 1, value: 20, holyPower: 18 },
         { level: 2, value: 40, holyPower: 36 },
         { level: 3, value: 60, holyPower: 54 } // Inferred progression
       ])
     ]
   },
   {
     nodeId: [34,35,36,37],
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with complete level progression data",
     availableStats: [
       createMythNodeStat("pvpIgnoreEvasion", 10, [
         { level: 1, value: 3, holyPower: 90 }, // Inferred holy power
         { level: 2, value: 4, holyPower: 95 }, // Inferred progression
         { level: 3, value: 5, holyPower: 100 },
         { level: 4, value: 6, holyPower: 105 }, // Inferred progression
         { level: 5, value: 7, holyPower: 110 },
         { level: 6, value: 9, holyPower: 125 }, // Inferred progression
         { level: 7, value: 11, holyPower: 140 }, // Inferred progression
         { level: 8, value: 13, holyPower: 155 }, // Inferred progression
         { level: 9, value: 15, holyPower: 170 }, // Inferred progression
         { level: 10, value: 18, holyPower: 240 }
       ]),
       createMythNodeStat("pveIgnoreEvasion", 10, [
         { level: 1, value: 3, holyPower: 90 }, // Inferred holy power
         { level: 2, value: 4, holyPower: 95 }, // Inferred progression
         { level: 3, value: 5, holyPower: 100 },
         { level: 4, value: 6, holyPower: 105 }, // Inferred progression
         { level: 5, value: 7, holyPower: 110 },
         { level: 6, value: 9, holyPower: 125 }, // Inferred progression
         { level: 7, value: 11, holyPower: 140 }, // Inferred progression
         { level: 8, value: 13, holyPower: 155 }, // Inferred progression
         { level: 9, value: 15, holyPower: 170 }, // Inferred progression
         { level: 10, value: 18, holyPower: 240 }
       ]),
       createMythNodeStat("pvpCancelIgnoreEvasion", 10, [
         { level: 1, value: 3, holyPower: 90 }, // Inferred holy power
         { level: 2, value: 4, holyPower: 95 }, // Inferred progression
         { level: 3, value: 5, holyPower: 100 }, // Inferred progression
         { level: 4, value: 6, holyPower: 105 },
         { level: 5, value: 7, holyPower: 110 },
         { level: 6, value: 9, holyPower: 125 }, // Inferred progression
         { level: 7, value: 11, holyPower: 140 }, // Inferred progression
         { level: 8, value: 12, holyPower: 150 }, // Inferred progression
         { level: 9, value: 14, holyPower: 160 },
         { level: 10, value: 18, holyPower: 240 } // Corrected from typo (was 8)
       ]),
       createMythNodeStat("pveCancelIgnoreEvasion", 10, [
         { level: 1, value: 3, holyPower: 90 }, // Inferred holy power
         { level: 2, value: 4, holyPower: 95 }, // Inferred progression
         { level: 3, value: 5, holyPower: 100 }, // Inferred progression
         { level: 4, value: 6, holyPower: 105 },
         { level: 5, value: 7, holyPower: 110 },
         { level: 6, value: 9, holyPower: 125 }, // Inferred progression
         { level: 7, value: 11, holyPower: 140 }, // Inferred progression
         { level: 8, value: 12, holyPower: 150 }, // Inferred progression
         { level: 9, value: 14, holyPower: 160 },
         { level: 10, value: 18, holyPower: 240 } // Corrected from typo (was 8)
       ]),
       createMythNodeStat("pvpIgnoreDamageReduce", 5, [
         { level: 1, value: 2, holyPower: 90 }, // Inferred holy power
         { level: 2, value: 3, holyPower: 106 },
         { level: 3, value: 4, holyPower: 128 },
         { level: 4, value: 6, holyPower: 150 }, // Inferred progression
         { level: 5, value: 9, holyPower: 180 } // Inferred progression
       ]),
       createMythNodeStat("pveIgnoreDamageReduce", 5, [
         { level: 1, value: 2, holyPower: 90 }, // Inferred holy power
         { level: 2, value: 3, holyPower: 106 },
         { level: 3, value: 4, holyPower: 128 },
         { level: 4, value: 6, holyPower: 150 }, // Inferred progression
         { level: 5, value: 9, holyPower: 180 } // Inferred progression
       ]),
       createMythNodeStat("pvpCancelIgnoreDamageReduce", 5, [
         { level: 1, value: 2, holyPower: 95 },
         { level: 2, value: 3, holyPower: 110 }, // Inferred progression
         { level: 3, value: 4, holyPower: 128 },
         { level: 4, value: 6, holyPower: 150 }, // Inferred progression
         { level: 5, value: 9, holyPower: 180 } // Inferred progression
       ]),
       createMythNodeStat("pveCancelIgnoreDamageReduce", 5, [
         { level: 1, value: 2, holyPower: 95 },
         { level: 2, value: 3, holyPower: 110 }, // Inferred progression
         { level: 3, value: 4, holyPower: 128 },
         { level: 4, value: 6, holyPower: 150 }, // Inferred progression
         { level: 5, value: 9, holyPower: 180 } // Inferred progression
       ]),
       createMythNodeStat("pvpDefense", 5, [
         { level: 1, value: 3, holyPower: 110 },
         { level: 2, value: 4, holyPower: 124 },
         { level: 3, value: 5, holyPower: 152 },
         { level: 4, value: 7, holyPower: 180 }, // Inferred progression
         { level: 5, value: 10, holyPower: 220 } // Inferred progression
       ]),
       createMythNodeStat("pveDefense", 5, [
         { level: 1, value: 3, holyPower: 110 },
         { level: 2, value: 4, holyPower: 124 },
         { level: 3, value: 5, holyPower: 152 },
         { level: 4, value: 7, holyPower: 180 }, // Inferred progression
         { level: 5, value: 10, holyPower: 220 } // Inferred progression
       ]),
       createMythNodeStat("pvpAllAttackUp", 5, [
         { level: 1, value: 3, holyPower: 110 },
         { level: 2, value: 5, holyPower: 140 }, // Inferred progression
         { level: 3, value: 7, holyPower: 170 }, // Inferred progression
         { level: 4, value: 8, holyPower: 190 }, // Inferred progression
         { level: 5, value: 10, holyPower: 220 } // Inferred progression
       ]),
       createMythNodeStat("pveAllAttackUp", 5, [
         { level: 1, value: 3, holyPower: 110 },
         { level: 2, value: 5, holyPower: 140 }, // Inferred progression
         { level: 3, value: 7, holyPower: 170 }, // Inferred progression
         { level: 4, value: 8, holyPower: 190 }, // Inferred progression
         { level: 5, value: 10, holyPower: 220 } // Inferred progression
       ])
     ]
   },
   {
     nodeId: [38,39],
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 38",
     availableStats: [
       createMythNodeStat("pvpIgnoreEvasion", 2, [
         { level: 1, value: 3, holyPower: 45 },
         { level: 2, value: 18, holyPower: 90 }
       ]),
       createMythNodeStat("pveIgnoreEvasion", 2, [
         { level: 1, value: 3, holyPower: 45 },
         { level: 2, value: 18, holyPower: 90 }
       ]),
       createMythNodeStat("pvpIgnoreDamageReduce", 2, [
         { level: 1, value: 2, holyPower: 35 },
         { level: 2, value: 9, holyPower: 70 }
       ]),
       createMythNodeStat("pveIgnoreDamageReduce", 2, [
         { level: 1, value: 2, holyPower: 35 },
         { level: 2, value: 9, holyPower: 70 }
       ]),
       createMythNodeStat("pvpAllAttackUp", 2, [
         { level: 1, value: 3, holyPower: 40 },
         { level: 2, value: 10, holyPower: 80 }
       ]),
       createMythNodeStat("pveAllAttackUp", 2, [
         { level: 1, value: 3, holyPower: 40 },
         { level: 2, value: 10, holyPower: 80 }
       ]),
       createMythNodeStat("pvpPenetration", 2, [
         { level: 1, value: 2, holyPower: 38 },
         { level: 2, value: 11, holyPower: 76 }
       ]),
       createMythNodeStat("pvePenetration", 2, [
         { level: 1, value: 2, holyPower: 38 },
         { level: 2, value: 11, holyPower: 76 }
       ])
     ]
   },
   {
     nodeId: 40,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 40",
     availableStats: [
       createMythNodeStat("pveIgnoreAccuracy", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 24, holyPower: 100 }
       ]),
       createMythNodeStat("pvpIgnoreAccuracy", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 24, holyPower: 100 }
       ]),
       createMythNodeStat("resistCritDmg", 2, [
         { level: 1, value: 3, holyPower: 45 },
         { level: 2, value: 4, holyPower: 90 }
       ]),
       createMythNodeStat("resistSkillAmp", 2, [
         { level: 1, value: 3, holyPower: 45 },
         { level: 2, value: 4, holyPower: 90 }
       ]),
       createMythNodeStat("hp", 2, [
         { level: 1, value: 30, holyPower: 60 },
         { level: 2, value: 55, holyPower: 120 }
       ])
     ]
   },
   {
     nodeId: 41,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 41 (Defensive)",
     availableStats: [
       createMythNodeStat("pveDefense", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 24, holyPower: 100 }
       ]),
       createMythNodeStat("pvpDefense", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 24, holyPower: 100 }
       ])
     ]
   },
   {
     nodeId: [42,44],
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 42 (Mixed)",
     availableStats: [
       createMythNodeStat("ignoreEvasion", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 24, holyPower: 100 }
       ]),
       createMythNodeStat("cancelIgnoreEvasion", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 24, holyPower: 100 }
       ]),
       createMythNodeStat("ignoreDamageReduce", 2, [
         { level: 1, value: 3, holyPower: 45 },
         { level: 2, value: 12, holyPower: 90 }
       ]),
       createMythNodeStat("cancelIgnoreDamageReduce", 2, [
         { level: 1, value: 3, holyPower: 45 },
         { level: 2, value: 12, holyPower: 90 }
       ]),
       createMythNodeStat("defense", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 13, holyPower: 100 }
       ]),
       createMythNodeStat("allAttackUp", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 13, holyPower: 100 }
       ])
     ]
   },
   {
     nodeId: 43,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 43 (Mixed)",
     availableStats: [
       createMythNodeStat("hp", 2, [
         { level: 1, value: 5, holyPower: 40 },
         { level: 2, value: 100, holyPower: 200 }
       ])
     ]
   },
   {
     nodeId: 45,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 45 (Offensive)",
     availableStats: [
       createMythNodeStat("pvpAllAttackUp", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 22, holyPower: 100 }
       ]),
       createMythNodeStat("pveAllAttackUp", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 22, holyPower: 100 }
       ])
     ]
   },
   {
     nodeId: 46,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 46 (Offensive)",
     availableStats: [
       createMythNodeStat("pvpIgnoreEvasion", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 24, holyPower: 100 }
       ]),
       createMythNodeStat("pveIgnoreEvasion", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 24, holyPower: 100 }
       ]),
       createMythNodeStat("pvpIgnoreDamageReduce", 2, [
         { level: 1, value: 3, holyPower: 45 },
         { level: 2, value: 12, holyPower: 90 }
       ]),
       createMythNodeStat("pveIgnoreDamageReduce", 2, [
         { level: 1, value: 3, holyPower: 45 },
         { level: 2, value: 12, holyPower: 90 }
       ]),
       createMythNodeStat("pvpAllAttackUp", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 13, holyPower: 100 }
       ]),
       createMythNodeStat("pveAllAttackUp", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 13, holyPower: 100 }
       ]),
       createMythNodeStat("pvpPenetration", 2, [
         { level: 1, value: 7, holyPower: 60 },
         { level: 2, value: 16, holyPower: 120 }
       ]),
       createMythNodeStat("pvePenetration", 2, [
         { level: 1, value: 7, holyPower: 60 },
         { level: 2, value: 16, holyPower: 120 }
       ])
     ]
   },
   {
     nodeId: [47,48],
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 47 (Defensive)",
     availableStats: [
       createMythNodeStat("pvpDefenseRate", 2, [
         { level: 1, value: 12, holyPower: 80 },
         { level: 2, value: 72, holyPower: 160 }
       ]),
       createMythNodeStat("pveDefenseRate", 2, [
         { level: 1, value: 12, holyPower: 80 },
         { level: 2, value: 72, holyPower: 160 }
       ]),
       createMythNodeStat("pvpCancelIgnoreDamageReduce", 2, [
         { level: 1, value: 5, holyPower: 50 },
         { level: 2, value: 12, holyPower: 100 }
       ]),
       createMythNodeStat("pveCancelIgnoreDamageReduce", 2, [
         { level: 1, value: 5, holyPower: 50 },
         { level: 2, value: 12, holyPower: 100 }
       ]),
       createMythNodeStat("hp", 2, [
         { level: 1, value: 25, holyPower: 100 },
         { level: 2, value: 45, holyPower: 200 }
       ]),
       createMythNodeStat("pveDefense", 2, [
         { level: 1, value: 5, holyPower: 50 },
         { level: 2, value: 12, holyPower: 100 }
       ]),
       createMythNodeStat("pvpDefense", 2, [
         { level: 1, value: 5, holyPower: 50 },
         { level: 2, value: 12, holyPower: 100 }
       ])
     ]
   },
   {
     nodeId: [49,50],
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 49 (Mixed)",
     availableStats: [
       createMythNodeStat("attackRate", 2, [
         { level: 1, value: 12, holyPower: 80 },
         { level: 2, value: 72, holyPower: 160 }
       ]),
       createMythNodeStat("defenseRate", 2, [
         { level: 1, value: 12, holyPower: 80 },
         { level: 2, value: 72, holyPower: 160 }
       ]),
       createMythNodeStat("hp", 2, [
         { level: 1, value: 25, holyPower: 100 },
         { level: 2, value: 45, holyPower: 200 }
       ]),
       createMythNodeStat("addDamage", 2, [
         { level: 1, value: 3, holyPower: 45 },
         { level: 2, value: 10, holyPower: 90 }
       ]),
       createMythNodeStat("damageReduce", 2, [
         { level: 1, value: 2, holyPower: 40 },
         { level: 2, value: 9, holyPower: 80 }
       ]),
       createMythNodeStat("penetration", 2, [
         { level: 1, value: 5, holyPower: 50 },
         { level: 2, value: 12, holyPower: 100 }
       ])
     ]
   },
   {
     nodeId: [51,52],
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 51 (Offensive)",
     availableStats: [
       createMythNodeStat("pvpAttackRate", 2, [
         { level: 1, value: 12, holyPower: 80 },
         { level: 2, value: 72, holyPower: 160 }
       ]),
       createMythNodeStat("pveAttackRate", 2, [
         { level: 1, value: 12, holyPower: 80 },
         { level: 2, value: 72, holyPower: 160 }
       ]),
       createMythNodeStat("pvpAddDamage", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 11, holyPower: 100 }
       ]),
       createMythNodeStat("pveAddDamage", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 11, holyPower: 100 }
       ]),
       createMythNodeStat("pvpAllAttackUp", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 11, holyPower: 100 }
       ]),
       createMythNodeStat("pveAllAttackUp", 2, [
         { level: 1, value: 4, holyPower: 50 },
         { level: 2, value: 11, holyPower: 100 }
       ]),
       createMythNodeStat("pvpIgnoreResistSkillAmp", 2, [
         { level: 1, value: 1, holyPower: 30 },
         { level: 2, value: 2, holyPower: 60 }
       ]),
       createMythNodeStat("pveIgnoreResistSkillAmp", 2, [
         { level: 1, value: 1, holyPower: 30 },
         { level: 2, value: 2, holyPower: 60 }
       ])
     ]
   },
   {
     nodeId: 53,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 53 (Defensive)",
     availableStats: [
       createMythNodeStat("pvpDefenseRate", 2, [
         { level: 1, value: 12, holyPower: 80 },
         { level: 2, value: 72, holyPower: 160 }
       ]),
       createMythNodeStat("pveDefenseRate", 2, [
         { level: 1, value: 12, holyPower: 80 },
         { level: 2, value: 72, holyPower: 160 }
       ]),
       createMythNodeStat("pvpCancelIgnoreDamageReduce", 2, [
         { level: 1, value: 5, holyPower: 50 },
         { level: 2, value: 12, holyPower: 100 }
       ]),
       createMythNodeStat("pveCancelIgnoreDamageReduce", 2, [
         { level: 1, value: 5, holyPower: 50 },
         { level: 2, value: 12, holyPower: 100 }
       ]),
       createMythNodeStat("hp", 2, [
         { level: 1, value: 25, holyPower: 100 },
         { level: 2, value: 45, holyPower: 200 }
       ]),
       createMythNodeStat("pveDefense", 2, [
         { level: 1, value: 5, holyPower: 50 },
         { level: 2, value: 12, holyPower: 100 }
       ]),
       createMythNodeStat("pvpDefense", 2, [
         { level: 1, value: 5, holyPower: 50 },
         { level: 2, value: 12, holyPower: 100 }
       ])
     ]
   },
   {
     nodeId: 54,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 54 (Defensive)",
     availableStats: [
       createMythNodeStat("pveIgnorePenetration", 2, [
         { level: 1, value: 3, holyPower: 45 },
         { level: 2, value: 20, holyPower: 90 }
       ]),
       createMythNodeStat("pvpIgnorePenetration", 2, [
         { level: 1, value: 3, holyPower: 45 },
         { level: 2, value: 20, holyPower: 90 }
       ])
     ]
   },
   {
     nodeId: [55, 57],
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 55 (Mixed)",
     availableStats: [
       createMythNodeStat("pveAttackRate", 2, [
         { level: 1, value: 14, holyPower: 35 },
         { level: 2, value: 94, holyPower: 60 }
       ]),
       createMythNodeStat("pvpAttackRate", 2, [
         { level: 1, value: 14, holyPower: 35 },
         { level: 2, value: 94, holyPower: 60 }
       ]),
       createMythNodeStat("pveDefenseRate", 2, [
         { level: 1, value: 14, holyPower: 35 },
         { level: 2, value: 94, holyPower: 60 }
       ]),
       createMythNodeStat("pvpDefenseRate", 2, [
         { level: 1, value: 14, holyPower: 35 },
         { level: 2, value: 94, holyPower: 60 }
       ]),
       createMythNodeStat("hp", 2, [
         { level: 1, value: 35, holyPower: 35 },
         { level: 2, value: 60, holyPower: 60 }
       ]),
       createMythNodeStat("pveAddDamage", 2, [
         { level: 1, value: 4, holyPower: 35 },
         { level: 2, value: 13, holyPower: 60 }
       ]),
       createMythNodeStat("pvpAddDamage", 2, [
         { level: 1, value: 4, holyPower: 35 },
         { level: 2, value: 13, holyPower: 60 }
       ]),
       createMythNodeStat("pveCancelIgnoreDamageReduce", 2, [
         { level: 1, value: 3, holyPower: 35 },
         { level: 2, value: 12, holyPower: 60 }
       ]),
       createMythNodeStat("pvpCancelIgnoreDamageReduce", 2, [
         { level: 1, value: 3, holyPower: 35 },
         { level: 2, value: 12, holyPower: 60 }
       ]),
       createMythNodeStat("pveIgnorePenetration", 2, [
         { level: 1, value: 7, holyPower: 35 },
         { level: 2, value: 16, holyPower: 60 }
       ]),
       createMythNodeStat("pvpIgnorePenetration", 2, [
         { level: 1, value: 7, holyPower: 35 },
         { level: 2, value: 16, holyPower: 60 }
       ])
     ]
   },
   {
     nodeId: 56,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 56 (Mixed)",
     availableStats: [
       createMythNodeStat("pveDefense", 2, [
         { level: 1, value: 4, holyPower: 35 },
         { level: 2, value: 30, holyPower: 60 }
       ]),
       createMythNodeStat("pvpDefense", 2, [
         { level: 1, value: 4, holyPower: 35 },
         { level: 2, value: 30, holyPower: 60 }
       ])
     ]
   },
   {
     nodeId: 58,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 58 (Offensive)",
     availableStats: [
       createMythNodeStat("pvpCancelIgnorePenetration", 2, [
         { level: 1, value: 3, holyPower: 35 },
         { level: 2, value: 20, holyPower: 60 }
       ]),
       createMythNodeStat("pveCancelIgnorePenetration", 2, [
         { level: 1, value: 3, holyPower: 35 },
         { level: 2, value: 20, holyPower: 60 }
       ])
     ]
   },
   {
     nodeId: 59,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 59 (Offensive)",
     availableStats: [
       createMythNodeStat("pvpAttackRate", 2, [
         { level: 1, value: 12, holyPower: 35 },
         { level: 2, value: 72, holyPower: 60 }
       ]),
       createMythNodeStat("pveAttackRate", 2, [
         { level: 1, value: 12, holyPower: 35 },
         { level: 2, value: 72, holyPower: 60 }
       ]),
       createMythNodeStat("pvpAddDamage", 2, [
         { level: 1, value: 4, holyPower: 35 },
         { level: 2, value: 11, holyPower: 60 }
       ]),
       createMythNodeStat("pveAddDamage", 2, [
         { level: 1, value: 4, holyPower: 35 },
         { level: 2, value: 11, holyPower: 60 }
       ]),
       createMythNodeStat("pvpAllAttackUp", 2, [
         { level: 1, value: 4, holyPower: 35 },
         { level: 2, value: 11, holyPower: 60 }
       ]),
       createMythNodeStat("pveAllAttackUp", 2, [
         { level: 1, value: 4, holyPower: 35 },
         { level: 2, value: 11, holyPower: 60 }
       ]),
       createMythNodeStat("pvpIgnoreResistSkillAmp", 2, [
         { level: 1, value: 1, holyPower: 35 },
         { level: 2, value: 2, holyPower: 60 }
       ]),
       createMythNodeStat("pveIgnoreResistSkillAmp", 2, [
         { level: 1, value: 1, holyPower: 35 },
         { level: 2, value: 2, holyPower: 60 }
       ])
     ]
   },
   {
     nodeId: 61,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 60 (Defensive)",
     availableStats: [
       createMythNodeStat("pvpEvasion", 2, [
         { level: 1, value: 4, holyPower: 40 },
         { level: 2, value: 64, holyPower: 65 }
       ]),
       createMythNodeStat("pveEvasion", 2, [
         { level: 1, value: 4, holyPower: 40 },
         { level: 2, value: 64, holyPower: 65 }
       ]),
       createMythNodeStat("pvpCancelIgnoreDamageReduce", 2, [
         { level: 1, value: 7, holyPower: 40 },
         { level: 2, value: 14, holyPower: 65 }
       ]),
       createMythNodeStat("pveCancelIgnoreDamageReduce", 2, [
         { level: 1, value: 7, holyPower: 40 },
         { level: 2, value: 14, holyPower: 65 }
       ]),
       createMythNodeStat("hp", 2, [
         { level: 1, value: 25, holyPower: 40 },
         { level: 2, value: 50, holyPower: 65 }
       ]),
       createMythNodeStat("pveIgnorePenetration", 2, [
         { level: 1, value: 5, holyPower: 40 },
         { level: 2, value: 16, holyPower: 65 }
       ]),
       createMythNodeStat("pvpIgnorePenetration", 2, [
         { level: 1, value: 5, holyPower: 40 },
         { level: 2, value: 16, holyPower: 65 }
       ])
     ]
   },
   {
     nodeId: [60, 62],
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 60 (Defensive)",
     availableStats: [
       createMythNodeStat("pvpEvasion", 2, [
         { level: 1, value: 8, holyPower: 40 },
         { level: 2, value: 84, holyPower: 65 }
       ]),
       createMythNodeStat("pveEvasion", 2, [
         { level: 1, value: 8, holyPower: 40 },
         { level: 2, value: 84, holyPower: 65 }
       ]),
       createMythNodeStat("pvpCancelIgnoreDamageReduce", 2, [
         { level: 1, value: 10, holyPower: 40 },
         { level: 2, value: 19, holyPower: 65 }
       ]),
       createMythNodeStat("pveCancelIgnoreDamageReduce", 2, [
         { level: 1, value: 10, holyPower: 40 },
         { level: 2, value: 19, holyPower: 65 }
       ]),
       createMythNodeStat("hp", 2, [
         { level: 1, value: 40, holyPower: 40 },
         { level: 2, value: 65, holyPower: 65 }
       ]),
       createMythNodeStat("pveIgnorePenetration", 2, [
         { level: 1, value: 12, holyPower: 40 },
         { level: 2, value: 21, holyPower: 65 }
       ]),
       createMythNodeStat("pvpIgnorePenetration", 2, [
         { level: 1, value: 12, holyPower: 40 },
         { level: 2, value: 21, holyPower: 65 }
       ])
     ]
   },
   {
     nodeId: [61, 70, 71],
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Shared configuration for nodes 61, 70, 71 - Defensive",
     availableStats: [
       createMythNodeStat("pvpEvasion", 2, [
         { level: 1, value: 4, holyPower: 40 },
         { level: 2, value: 64, holyPower: 65 }
       ]),
       createMythNodeStat("pveEvasion", 2, [
         { level: 1, value: 4, holyPower: 40 },
         { level: 2, value: 64, holyPower: 65 }
       ]),
       createMythNodeStat("pvpCancelIgnoreDamageReduce", 2, [
         { level: 1, value: 7, holyPower: 40 },
         { level: 2, value: 14, holyPower: 65 }
       ]),
       createMythNodeStat("pveCancelIgnoreDamageReduce", 2, [
         { level: 1, value: 7, holyPower: 40 },
         { level: 2, value: 14, holyPower: 65 }
       ]),
       createMythNodeStat("hp", 2, [
         { level: 1, value: 25, holyPower: 40 },
         { level: 2, value: 50, holyPower: 65 }
       ]),
       createMythNodeStat("pveIgnorePenetration", 2, [
         { level: 1, value: 5, holyPower: 40 },
         { level: 2, value: 16, holyPower: 65 }
       ]),
       createMythNodeStat("pvpIgnorePenetration", 2, [
         { level: 1, value: 5, holyPower: 40 },
         { level: 2, value: 16, holyPower: 65 }
       ])
     ]
   },
   {
     nodeId: 63,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Updated with data from image - node 63 (Mixed)",
     availableStats: [
       createMythNodeStat("evasion", 2, [
         { level: 1, value: 4, holyPower: 40 },
         { level: 2, value: 64, holyPower: 65 }
       ]),
       createMythNodeStat("accuracy", 2, [
         { level: 1, value: 15, holyPower: 40 },
         { level: 2, value: 30, holyPower: 65 }
       ]),
       createMythNodeStat("hp", 2, [
         { level: 1, value: 25, holyPower: 40 },
         { level: 2, value: 50, holyPower: 65 }
       ]),
       createMythNodeStat("normalDamageUp", 2, [
         { level: 1, value: 1, holyPower: 40 },
         { level: 2, value: 5, holyPower: 65 }
       ]),
       createMythNodeStat("defense", 2, [
         { level: 1, value: 7, holyPower: 40 },
         { level: 2, value: 14, holyPower: 65 }
       ]),
       createMythNodeStat("critDamage", 2, [
         { level: 1, value: 1, holyPower: 40 },
         { level: 2, value: 4, holyPower: 65 }
       ])
     ]
   },
   {
     nodeId: 67,
     isDataComplete: false,
     lastUpdated: new Date().toISOString(),
     notes: "Same stats as node 62 - Defensive",
     availableStats: [
       createMythNodeStat("pvpEvasion", 2, [
         { level: 1, value: 8, holyPower: 40 },
         { level: 2, value: 84, holyPower: 65 }
       ]),
       createMythNodeStat("pveEvasion", 2, [
         { level: 1, value: 8, holyPower: 40 },
         { level: 2, value: 84, holyPower: 65 }
       ]),
       createMythNodeStat("pvpCancelIgnoreDamageReduce", 2, [
         { level: 1, value: 10, holyPower: 40 },
         { level: 2, value: 19, holyPower: 65 }
       ]),
       createMythNodeStat("pveCancelIgnoreDamageReduce", 2, [
         { level: 1, value: 10, holyPower: 40 },
         { level: 2, value: 19, holyPower: 65 }
       ]),
       createMythNodeStat("hp", 2, [
         { level: 1, value: 40, holyPower: 40 },
         { level: 2, value: 65, holyPower: 65 }
       ]),
       createMythNodeStat("pveIgnorePenetration", 2, [
         { level: 1, value: 12, holyPower: 40 },
         { level: 2, value: 21, holyPower: 65 }
       ]),
       createMythNodeStat("pvpIgnorePenetration", 2, [
         { level: 1, value: 12, holyPower: 40 },
         { level: 2, value: 21, holyPower: 65 }
       ])
     ]
   },
   {
     nodeId: 68,
     isDataComplete: false,
     lastUpdated: "2024-12-19",
     notes: "Updated with data from image",
     availableStats: [
       createMythNodeStat("evasion", 2, [
         { level: 1, value: 8, holyPower: 40 },
         { level: 2, value: 84, holyPower: 65 }
       ]),
       createMythNodeStat("accuracy", 2, [
         { level: 1, value: 25, holyPower: 40 },
         { level: 2, value: 40, holyPower: 65 }
       ]),
       createMythNodeStat("hp", 2, [
         { level: 1, value: 40, holyPower: 40 },
         { level: 2, value: 65, holyPower: 65 }
       ]),
       createMythNodeStat("normalDamageUp", 2, [
         { level: 1, value: 1, holyPower: 40 },
         { level: 2, value: 7, holyPower: 65 }
       ]),
       createMythNodeStat("defense", 2, [
         { level: 1, value: 10, holyPower: 40 },
         { level: 2, value: 19, holyPower: 65 }
       ]),
       createMythNodeStat("critDamage", 2, [
         { level: 1, value: 2, holyPower: 40 },
         { level: 2, value: 6, holyPower: 65 }
       ])
     ]
   },
   {
     nodeId: [72, 73],
     isDataComplete: false,
     lastUpdated: "2024-12-19",
     notes: "Updated with data from image - Mixed stats",
     availableStats: [
       createMythNodeStat("evasion", 2, [
         { level: 1, value: 4, holyPower: 40 },
         { level: 2, value: 64, holyPower: 65 }
       ]),
       createMythNodeStat("accuracy", 2, [
         { level: 1, value: 15, holyPower: 40 },
         { level: 2, value: 30, holyPower: 65 }
       ]),
       createMythNodeStat("hp", 2, [
         { level: 1, value: 25, holyPower: 40 },
         { level: 2, value: 50, holyPower: 65 }
       ]),
       createMythNodeStat("normalDamageUp", 2, [
         { level: 1, value: 1, holyPower: 40 },
         { level: 2, value: 5, holyPower: 65 }
       ]),
       createMythNodeStat("defense", 2, [
         { level: 1, value: 7, holyPower: 40 },
         { level: 2, value: 14, holyPower: 65 }
       ]),
       createMythNodeStat("critDamage", 2, [
         { level: 1, value: 1, holyPower: 40 },
         { level: 2, value: 4, holyPower: 65 }
       ])
     ]
   },
   {
     nodeId: [64, 65, 66, 69],
     isDataComplete: false,
     lastUpdated: "2024-12-19",
     notes: "Updated with data from image - Offensive stats",
     availableStats: [
       createMythNodeStat("pvpAccuracy", 2, [
         { level: 1, value: 25, holyPower: 40 },
         { level: 2, value: 40, holyPower: 65 }
       ]),
       createMythNodeStat("pveAccuracy", 2, [
         { level: 1, value: 25, holyPower: 40 },
         { level: 2, value: 40, holyPower: 65 }
       ]),
       createMythNodeStat("pvpNormalDamageUp", 2, [
         { level: 1, value: 1, holyPower: 40 },
         { level: 2, value: 7, holyPower: 65 }
       ]),
       createMythNodeStat("pveNormalDamageUp", 2, [
         { level: 1, value: 1, holyPower: 40 },
         { level: 2, value: 7, holyPower: 65 }
       ]),
       createMythNodeStat("pvpAllAttackUp", 2, [
         { level: 1, value: 7, holyPower: 40 },
         { level: 2, value: 16, holyPower: 65 }
       ]),
       createMythNodeStat("pveAllAttackUp", 2, [
         { level: 1, value: 7, holyPower: 40 },
         { level: 2, value: 16, holyPower: 65 }
       ]),
       createMythNodeStat("pvpCritDamage", 2, [
         { level: 1, value: 2, holyPower: 40 },
         { level: 2, value: 6, holyPower: 65 }
       ]),
       createMythNodeStat("pveCritDamage", 2, [
         { level: 1, value: 2, holyPower: 40 },
         { level: 2, value: 6, holyPower: 65 }
       ])
     ]
   },
   {
     nodeId: 74,
     isDataComplete: false,
     lastUpdated: "2024-12-19",
     notes: "Updated with data from image - Offensive stats",
     availableStats: [
       createMythNodeStat("pvpAccuracy", 2, [
         { level: 1, value: 15, holyPower: 40 },
         { level: 2, value: 30, holyPower: 65 }
       ]),
       createMythNodeStat("pveAccuracy", 2, [
         { level: 1, value: 15, holyPower: 40 },
         { level: 2, value: 30, holyPower: 65 }
       ]),
       createMythNodeStat("pvpNormalDamageUp", 2, [
         { level: 1, value: 1, holyPower: 40 },
         { level: 2, value: 5, holyPower: 65 }
       ]),
       createMythNodeStat("pveNormalDamageUp", 2, [
         { level: 1, value: 1, holyPower: 40 },
         { level: 2, value: 5, holyPower: 65 }
       ]),
       createMythNodeStat("pvpAllAttackUp", 2, [
         { level: 1, value: 5, holyPower: 40 },
         { level: 2, value: 12, holyPower: 65 }
       ]),
       createMythNodeStat("pveAllAttackUp", 2, [
         { level: 1, value: 5, holyPower: 40 },
         { level: 2, value: 12, holyPower: 65 }
       ]),
       createMythNodeStat("pvpCritDamage", 2, [
         { level: 1, value: 1, holyPower: 40 },
         { level: 2, value: 4, holyPower: 65 }
       ]),
       createMythNodeStat("pveCritDamage", 2, [
         { level: 1, value: 1, holyPower: 40 },
         { level: 2, value: 4, holyPower: 65 }
       ])
     ]
   },
   {
     nodeId: 75,
     isDataComplete: false,
     lastUpdated: "2024-12-19",
     notes: "Updated with data from image - Offensive stats",
     availableStats: [
       createMythNodeStat("pvpAccuracy", 2, [
         { level: 1, value: 15, holyPower: 40 },
         { level: 2, value: 30, holyPower: 65 }
       ]),
       createMythNodeStat("pveAccuracy", 2, [
         { level: 1, value: 15, holyPower: 40 },
         { level: 2, value: 30, holyPower: 65 }
       ]),
       createMythNodeStat("pvpNormalDamageUp", 2, [
         { level: 1, value: 1, holyPower: 40 },
         { level: 2, value: 5, holyPower: 65 }
       ]),
       createMythNodeStat("pveNormalDamageUp", 2, [
         { level: 1, value: 1, holyPower: 40 },
         { level: 2, value: 5, holyPower: 65 }
       ]),
       createMythNodeStat("pvpAllAttackUp", 2, [
         { level: 1, value: 5, holyPower: 40 },
         { level: 2, value: 12, holyPower: 65 }
       ]),
       createMythNodeStat("pveAllAttackUp", 2, [
         { level: 1, value: 5, holyPower: 40 },
         { level: 2, value: 12, holyPower: 65 }
       ]),
       createMythNodeStat("pvpCritDamage", 2, [
         { level: 1, value: 1, holyPower: 40 },
         { level: 2, value: 4, holyPower: 65 }
       ]),
       createMythNodeStat("pveCritDamage", 2, [
         { level: 1, value: 1, holyPower: 40 },
         { level: 2, value: 4, holyPower: 65 }
       ])
     ]
   },
   {
     nodeId: 78,
     isDataComplete: false,
     lastUpdated: "2024-12-19",
     notes: "Updated with data from image - Offensive stats",
     availableStats: [
       createMythNodeStat("pvpNormalDamageUp", 2, [
         { level: 1, value: 1, holyPower: 40 },
         { level: 2, value: 8, holyPower: 65 }
       ]),
       createMythNodeStat("pveNormalDamageUp", 2, [
         { level: 1, value: 1, holyPower: 40 },
         { level: 2, value: 8, holyPower: 65 }
       ]),
       createMythNodeStat("pvpAllAttackUp", 2, [
         { level: 1, value: 15, holyPower: 40 },
         { level: 2, value: 80, holyPower: 65 }
       ]),
       createMythNodeStat("pveAllAttackUp", 2, [
         { level: 1, value: 15, holyPower: 40 },
         { level: 2, value: 80, holyPower: 65 }
       ]),
       createMythNodeStat("pvpPenetration", 2, [
         { level: 1, value: 5, holyPower: 40 },
         { level: 2, value: 45, holyPower: 65 }
       ]),
       createMythNodeStat("pvePenetration", 2, [
         { level: 1, value: 5, holyPower: 40 },
         { level: 2, value: 45, holyPower: 65 }
       ]),
       createMythNodeStat("pvpAllSkillAmp", 2, [
         { level: 1, value: 2, holyPower: 40 },
         { level: 2, value: 7, holyPower: 65 }
       ]),
       createMythNodeStat("pveAllSkillAmp", 2, [
         { level: 1, value: 2, holyPower: 40 },
         { level: 2, value: 7, holyPower: 65 }
       ])
     ]
   },
   {
     nodeId: 76,
     isDataComplete: false,
     lastUpdated: "2024-12-19",
     notes: "Updated with data from image - Defensive stats",
     availableStats: [
       createMythNodeStat("pvpDamageReduce", 2, [
         { level: 1, value: 5, holyPower: 40 },
         { level: 2, value: 30, holyPower: 65 }
       ]),
       createMythNodeStat("pveDamageReduce", 2, [
         { level: 1, value: 5, holyPower: 40 },
         { level: 2, value: 30, holyPower: 65 }
       ]),
       createMythNodeStat("hp", 2, [
         { level: 1, value: 40, holyPower: 40 },
         { level: 2, value: 300, holyPower: 65 }
       ]),
       createMythNodeStat("pveDefense", 2, [
         { level: 1, value: 15, holyPower: 40 },
         { level: 2, value: 100, holyPower: 65 }
       ]),
       createMythNodeStat("pvpDefense", 2, [
         { level: 1, value: 15, holyPower: 40 },
         { level: 2, value: 100, holyPower: 65 }
       ]),
       createMythNodeStat("pveIgnorePenetration", 2, [
         { level: 1, value: 10, holyPower: 40 },
         { level: 2, value: 60, holyPower: 65 }
       ]),
       createMythNodeStat("pvpIgnorePenetration", 2, [
         { level: 1, value: 10, holyPower: 40 },
         { level: 2, value: 60, holyPower: 65 }
       ])
     ]
   }
];

// Helper functions
export const getNodeData = (nodeId: number): MythLevelNodeData | undefined => {
  return mythLevelNodeData.find(node => {
    if (Array.isArray(node.nodeId)) {
      return node.nodeId.includes(nodeId);
    }
    return node.nodeId === nodeId;
  });
};

export const getNodeStat = (nodeId: number, statKey: string): MythNodeStat | undefined => {
  const nodeData = getNodeData(nodeId);
  return nodeData?.availableStats.find(stat => stat.statKey === statKey);
};

export const getStatLevelData = (nodeId: number, statKey: string, level: number): StatLevelData | undefined => {
  const stat = getNodeStat(nodeId, statKey);
  return stat?.levels.find(l => l.level === level);
};

export const getAllAvailableStats = (nodeId: number): MythNodeStat[] => {
  const nodeData = getNodeData(nodeId);
  return nodeData?.availableStats || [];
};

export const getStatValueRange = (nodeId: number, statKey: string): { min: number; max: number } | null => {
  const stat = getNodeStat(nodeId, statKey);
  if (!stat) return null;
  
  return {
    min: stat.minValue,
    max: stat.maxValue
  };
};

export const getHolyPowerRange = (nodeId: number, statKey: string): { min: number; max: number } | null => {
  const stat = getNodeStat(nodeId, statKey);
  if (!stat || stat.levels.length === 0) return null;
  
  const holyPowers = stat.levels.map(l => l.holyPower);
  return {
    min: Math.min(...holyPowers),
    max: Math.max(...holyPowers)
  };
};

// Validation helpers
export const validateNodeData = (nodeData: MythLevelNodeData): string[] => {
  const errors: string[] = [];
  
  // Validate nodeId
  const nodeIds = getNodeIds(nodeData);
  if (nodeIds.length === 0) {
    errors.push("Node must have at least one node ID");
  }
  
  // Check for duplicate node IDs within the same entry
  if (Array.isArray(nodeData.nodeId)) {
    const uniqueIds = new Set(nodeData.nodeId);
    if (nodeData.nodeId.length !== uniqueIds.size) {
      errors.push("Node has duplicate IDs in the same entry");
    }
  }
  
  if (nodeData.availableStats.length === 0) {
    errors.push("Node must have at least one available stat");
  }
  
  nodeData.availableStats.forEach((stat, index) => {
    if (stat.levels.length === 0) {
      errors.push(`Stat ${stat.name} has no level data`);
    }
    
    if (stat.levels.length !== stat.maxLevel) {
      errors.push(`Stat ${stat.name} maxLevel (${stat.maxLevel}) doesn't match levels array length (${stat.levels.length})`);
    }
    
    // Check for duplicate levels
    const levelNumbers = stat.levels.map(l => l.level);
    const uniqueLevels = new Set(levelNumbers);
    if (levelNumbers.length !== uniqueLevels.size) {
      errors.push(`Stat ${stat.name} has duplicate level entries`);
    }
    
    // Check level sequence
    const sortedLevels = [...levelNumbers].sort((a, b) => a - b);
    for (let i = 0; i < sortedLevels.length; i++) {
      if (sortedLevels[i] !== i + 1) {
        errors.push(`Stat ${stat.name} has missing or invalid level sequence`);
        break;
      }
    }
  });
  
  return errors;
};

export const getTotalNodesWithData = (): number => {
  return mythLevelNodeData.length;
};

export const getCompleteNodesCount = (): number => {
  return mythLevelNodeData.filter(node => node.isDataComplete).length;
};

// Helper to get all node IDs from a node data entry
export const getNodeIds = (nodeData: MythLevelNodeData): number[] => {
  return Array.isArray(nodeData.nodeId) ? nodeData.nodeId : [nodeData.nodeId];
};

// Helper to get all unique node IDs across all data
export const getAllNodeIds = (): number[] => {
  const allIds: number[] = [];
  mythLevelNodeData.forEach(nodeData => {
    allIds.push(...getNodeIds(nodeData));
  });
  return [...new Set(allIds)].sort((a, b) => a - b);
};

// Validate that no node ID appears in multiple entries
export const validateUniqueNodeIds = (): string[] => {
  const errors: string[] = [];
  const seenIds = new Map<number, number>(); // nodeId -> index of first occurrence
  
  mythLevelNodeData.forEach((nodeData, index) => {
    const nodeIds = getNodeIds(nodeData);
    nodeIds.forEach(nodeId => {
      if (seenIds.has(nodeId)) {
        errors.push(`Node ID ${nodeId} appears in multiple entries (indices ${seenIds.get(nodeId)} and ${index})`);
      } else {
        seenIds.set(nodeId, index);
      }
    });
  });
  
  return errors;
};