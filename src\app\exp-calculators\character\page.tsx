import Link from 'next/link';
import CharacterExpCalculator from '@/tools/exp-calculators/CharacterExpCalculator';

export default function CharacterExpCalculatorPage() {
  return (
    <div className="min-h-screen text-foreground p-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <Link href="/exp-calculators" className="text-blue-400 hover:text-blue-300 text-sm">
            ← Back to EXP Calculators
          </Link>
          <h1 className="text-4xl font-bold mt-4 mb-4">Character EXP Calculator</h1>
          <p className="text-foreground/80">
            Calculate experience needed to reach your target character level.
          </p>
        </div>
        
        <CharacterExpCalculator />
      </div>
    </div>
  );
}