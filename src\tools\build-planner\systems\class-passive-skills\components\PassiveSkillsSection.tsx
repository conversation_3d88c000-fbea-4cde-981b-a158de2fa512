import React from 'react';
import { useClassPassiveSkillsStore } from '../stores';
import { getPassiveSkillsForClass } from '../data/classPassiveSkills';
import { PassiveSkillSlot } from './PassiveSkillSlot';

export const PassiveSkillsSection: React.FC = () => {
  const { selectedClass, passiveSkills, setPassiveSkillLevel, resetPassiveSkills } = useClassPassiveSkillsStore();

  if (!selectedClass) {
    return null;
  }

  const availableSkills = getPassiveSkillsForClass(selectedClass);

  if (availableSkills.length === 0) {
    return (
      <div className="bg-[#1e1e28b3] rounded-lg p-4 border border-[#2a2a3a]" style={{ width: '600px' }}>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-game-gold">Class Passive Skills</h3>
        </div>
        <div className="text-center text-gray-400 py-8">
          <p>No passive skills available for this class yet.</p>
          <p className="text-sm mt-2">Passive skills will be added in future updates.</p>
        </div>
      </div>
    );
  }

  const hasActiveSkills = Object.values(passiveSkills).some(level => level > 0);

  return (
    <div className="bg-[#1e1e28b3] rounded-lg p-4 border border-[#2a2a3a]" style={{ width: 'fit-content', minWidth: '600px', maxWidth: '1200px' }}>
      {/* Section Header */}
      <div className="flex justify-between items-center mb-4">
        <div>
          <h3 className="text-lg font-semibold text-game-gold">Class Passive Skills</h3>
          <p className="text-sm text-gray-400">
            Class-specific passive abilities that enhance your character
          </p>
        </div>
        
        {hasActiveSkills && (
          <button
            onClick={resetPassiveSkills}
            className="bg-red-600/20 hover:bg-red-600/30 text-red-400 px-3 py-1 rounded text-sm transition-colors border border-red-600/30"
          >
            Reset All
          </button>
        )}
      </div>

      {/* Skills Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {availableSkills.map((skill) => (
          <PassiveSkillSlot
            key={skill.id}
            skill={skill}
            currentLevel={passiveSkills[skill.id] || 0}
            onLevelChange={setPassiveSkillLevel}
          />
        ))}
      </div>

      {/* Info Text */}
      <div className="mt-4 text-xs text-gray-500 bg-[#1a1a24] rounded p-3">
        <p>
          <strong>Note:</strong> Class passive skills provide permanent stat bonuses specific to your selected class. 
          Each skill can be leveled up to increase its effectiveness. The stats from passive skills 
          are automatically included in your total character stats.
        </p>
      </div>
    </div>
  );
};