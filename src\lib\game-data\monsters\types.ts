/**
 * Shared Monster Data Types
 * Used by both Build Planner and Mob Table tools
 */

export interface RawMonsterData {
  ID: string;
  Dungeon_ID: string;
  Name: string;
  Level: string;
  DisplayHP: string;
  IsABoss: boolean;
  Stats: {
    Level: number;
    HP: number;
    "HP Recharge": number;
    "Attack Rate": number;
    "Defense Rate": number;
    Defense: number;
    "Default Skill Physical Attack Min": number;
    "Default Skill Physical Attack Max": number;
    "Default Skill Reach": number;
    "Default Skill Range": number;
    "Default Skill Interval": number;
    "Special Skill Physical Attack Min": number;
    "Special Skill Physical Attack Max": number;
    "Special Skill Reach": number;
    "Special Skill Range": number;
    "Special Skill Interval": number;
    "Chase Range": number;
    Exp: number;
    "Default Skill Stance": number;
    "Special Skill Stance": number;
    "Special Skill Group": number;
    "Damage Reduction": number;
    Accuracy: number;
    Penetration: number;
    "Resist Critical Rate": number;
    "Attack Count Amp": number;
    "Ignore Accuracy": number;
    "Ignore Damage Reduction": number;
    "Ignore Penetration": number;
    "Absolute Damage": number;
    "Resist Skill Amp": number;
    "Resist Critical Damage": number;
    "Resist Silence": number;
    "HP Dmg Prop": number;
    "Is World Boss": number;
  };
}

export interface MonsterStats {
  id: string;
  dungeonId: string;
  name: string;
  level: number;
  isABoss: boolean;
  hp: number;
  hpRecharge: number;
  attackRate: number;
  defenseRate: number;
  defense: number;
  defaultSkillPhysicalAttackMin: number;
  defaultSkillPhysicalAttackMax: number;
  defaultSkillReach: number;
  defaultSkillRange: number;
  defaultSkillInterval: number;
  specialSkillPhysicalAttackMin: number;
  specialSkillPhysicalAttackMax: number;
  specialSkillReach: number;
  specialSkillRange: number;
  specialSkillInterval: number;
  chaseRange: number;
  exp: number;
  defaultSkillStance: number;
  specialSkillStance: number;
  specialSkillGroup: number;
  damageReduction: number;
  accuracy: number;
  penetration: number;
  resistCriticalRate: number;
  attackCountAmp: number;
  ignoreAccuracy: number;
  ignoreDamageReduction: number;
  ignorePenetration: number;
  absoluteDamage: number;
  resistSkillAmp: number;
  resistCriticalDamage: number;
  resistSilence: number;
  hpDmgProp: number;
  isWorldBoss: number;
}

export interface MonsterSearchFilters {
  minLevel?: number;
  maxLevel?: number;
  bossOnly?: boolean;
  dungeonId?: string;
}