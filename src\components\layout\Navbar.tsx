'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import { FiMenu, FiX } from 'react-icons/fi';
import { cn } from '../../utils/cn';

interface NavigationItem {
  name: string;
  href: string;
  subItems?: (SubItem | CategoryItem)[];
}

interface SubItem {
  name: string;
  href: string;
}

interface CategoryItem {
  name: string;
  href: string;
  isCategory: true;
  items: SubItem[];
}

// Navigation items based on the migration plan
const navigationItems: NavigationItem[] = [
  { name: 'Build Planner', href: '/build-planner' },
  { name: 'Tier Lists', href: '/tier-lists' },
  { name: 'Monster Database', href: '/mob-table' },
  { 
    name: 'Calculators', 
    href: '/calculators',
    subItems: [
      { 
        name: 'Profit Calculators', 
        href: '/profit-calculators',
        isCategory: true,
        items: [
          { name: 'Devil Shop', href: '/devil-shop-calculator' },
          { name: '<PERSON> Craft', href: '/chloe-calculator' },
        ]
      },
      { 
        name: 'Other Calculators', 
        href: '/other-calculators',
        isCategory: true,
        items: [
          { name: 'Amity Calculator', href: '/chloe-calculators/amity-craft' },
        ]
      },
      { 
        name: 'EXP Calculators', 
        href: '/exp-calculators',
        isCategory: true,
        items: [
          { name: 'Character EXP', href: '/exp-calculators/character' },
          { name: 'Force Wing', href: '/exp-calculators/force-wing' },
          { name: 'OXP', href: '/exp-calculators/oxp' },
        ]
      },
    ]
  },
  { name: 'Stats Wiki', href: '/stats-wiki' },
];

export default function Navbar() {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);

  const toggleSubmenu = (name: string) => {
    setOpenSubmenu(openSubmenu === name ? null : name);
  };

  return (
    <nav className="border-b border-border-light" style={{ 
      background: 'linear-gradient(135deg, rgb(20, 20, 30), rgb(16, 16, 24))'
    }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo on the left */}
          <div className="flex-shrink-0 flex items-center">
            <Link href="/" className="text-2xl font-bold text-game-gold hover:text-game-highlight">
              Nipperlug
            </Link>
          </div>
          
          {/* Centered desktop navigation */}
          <div className="hidden sm:flex sm:flex-1 sm:justify-center sm:items-center">
            <div className="flex items-center">
              {navigationItems.map((item) => (
                <div key={item.name} className="relative group">
                  <Link
                    href={item.href}
                    className={cn(
                      "px-6 py-4 text-sm font-medium",
                      pathname === item.href
                        ? "text-game-highlight bg-black/20"
                        : "text-foreground hover:text-game-gold hover:bg-white/5"
                    )}
                  >
                    {item.name}
                    {item.subItems && (
                      <span className="ml-1 text-xs opacity-70">▼</span>
                    )}
                  </Link>
                  
                  {/* Dropdown for desktop */}
                  {item.subItems && (
                    <div className="absolute left-0 top-full w-64 hidden group-hover:block z-50" style={{ 
                      background: 'linear-gradient(135deg, rgb(20, 20, 30), rgb(16, 16, 24))',
                      border: '1px solid var(--border-light)'
                    }}>
                      <div className="py-1">
                        {item.subItems.map((subItem) => (
                          <div key={subItem.name}>
                            {'isCategory' in subItem ? (
                              <div>
                                <div className="px-4 py-2 text-xs font-semibold text-game-gold uppercase tracking-wide border-b border-border-dark">
                                  {subItem.name}
                                </div>
                                {subItem.items.map((categoryItem: SubItem) => (
                                  <Link
                                    key={categoryItem.name}
                                    href={categoryItem.href}
                                    className={cn(
                                      "block px-6 py-2 text-sm pl-8",
                                      pathname === categoryItem.href
                                        ? "text-game-highlight bg-black/20"
                                        : "text-foreground hover:text-game-gold hover:bg-white/5"
                                    )}
                                  >
                                    {categoryItem.name}
                                  </Link>
                                ))}
                              </div>
                            ) : (
                              <Link
                                href={subItem.href}
                                className={cn(
                                  "block px-4 py-3 text-sm",
                                  pathname === subItem.href
                                    ? "text-game-highlight bg-black/20"
                                    : "text-foreground hover:text-game-gold hover:bg-white/5"
                                )}
                              >
                                {subItem.name}
                              </Link>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
          
          {/* Empty div to balance the layout (same width as logo) */}
          <div className="hidden sm:flex sm:items-center sm:flex-shrink-0">
            <div className="w-[110px]"></div> {/* Approximate width of the logo */}
          </div>
          
          {/* Mobile menu button */}
          <div className="flex items-center sm:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="inline-flex items-center justify-center p-3 text-foreground hover:text-game-gold hover:bg-white/5 focus:outline-none"
            >
              <span className="sr-only">Open main menu</span>
              {isOpen ? (
                <FiX className="block h-6 w-6" aria-hidden="true" />
              ) : (
                <FiMenu className="block h-6 w-6" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isOpen && (
        <div className="sm:hidden" style={{ 
          background: 'linear-gradient(135deg, rgb(20, 20, 30), rgb(16, 16, 24))',
          borderTop: '1px solid var(--border-light)'
        }}>
          <div className="px-4 pt-2 pb-4 space-y-1">
            {navigationItems.map((item) => (
              <div key={item.name}>
                <div className="flex items-center justify-between">
                  <Link
                    href={item.href}
                    className={cn(
                      "block px-4 py-4 text-base font-medium",
                      pathname === item.href
                        ? "text-game-highlight bg-black/20"
                        : "text-foreground hover:text-game-gold hover:bg-white/5"
                    )}
                    onClick={() => {
                      if (!item.subItems) {
                        setIsOpen(false);
                      }
                    }}
                  >
                    {item.name}
                  </Link>
                  {item.subItems && (
                    <button
                      onClick={() => toggleSubmenu(item.name)}
                      className="px-4 py-4 text-foreground hover:text-game-gold hover:bg-white/5"
                      aria-label={`Toggle ${item.name} submenu`}
                    >
                      {openSubmenu === item.name ? '▲' : '▼'}
                    </button>
                  )}
                </div>
                
                {/* Mobile submenu */}
                {item.subItems && openSubmenu === item.name && (
                  <div className="pl-8 space-y-1 border-l border-border-dark ml-4">
                    {item.subItems.map((subItem) => (
                      <div key={subItem.name}>
                        {'isCategory' in subItem ? (
                          <div>
                            <div className="px-4 py-2 text-xs font-semibold text-game-gold uppercase tracking-wide">
                              {subItem.name}
                            </div>
                            {subItem.items.map((categoryItem: SubItem) => (
                              <Link
                                key={categoryItem.name}
                                href={categoryItem.href}
                                className={cn(
                                  "block px-6 py-2 text-sm font-medium pl-8",
                                  pathname === categoryItem.href
                                    ? "text-game-highlight bg-black/20"
                                    : "text-foreground hover:text-game-gold hover:bg-white/5"
                                )}
                                onClick={() => setIsOpen(false)}
                              >
                                {categoryItem.name}
                              </Link>
                            ))}
                          </div>
                        ) : (
                          <Link
                            href={subItem.href}
                            className={cn(
                              "block px-4 py-3 text-sm font-medium",
                              pathname === subItem.href
                                ? "text-game-highlight bg-black/20"
                                : "text-foreground hover:text-game-gold hover:bg-white/5"
                            )}
                            onClick={() => setIsOpen(false)}
                          >
                            {subItem.name}
                          </Link>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </nav>
  );
}