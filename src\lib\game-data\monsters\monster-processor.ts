/**
 * Shared Monster Data Processor
 * Efficiently processes and searches through the large monster dataset
 * Used by both Build Planner and Mob Table tools
 */

import { RawMonsterData, MonsterStats, MonsterSearchFilters } from './types';

// Search index interface for efficient searching
interface MonsterSearchIndex {
  id: string;
  name: string;
  level: number;
  isBoss: boolean;
  searchText: string;
}

// Cache for processed monsters to avoid reprocessing
const processedMonstersCache = new Map<string, MonsterStats>();
const searchIndexCache: MonsterSearchIndex[] = [];
let isIndexBuilt = false;

/**
 * Transform raw monster data to our application format
 */
export function processRawMonster(rawMonster: RawMonsterData, index?: number): MonsterStats {
  // Create unique cache key using both ID and Dungeon_ID to handle duplicates
  const cacheKey = `${rawMonster.ID}-${rawMonster.Dungeon_ID}`;
  
  // Return cached version if available
  if (processedMonstersCache.has(cacheKey)) {
    return processedMonstersCache.get(cacheKey)!;
  }

  const processed: MonsterStats = {
    // Create unique ID by combining ID and Dungeon_ID, or use index as fallback
    id: rawMonster.Dungeon_ID ? `${rawMonster.ID}-${rawMonster.Dungeon_ID}` : `${rawMonster.ID}-${index || 0}`,
    dungeonId: rawMonster.Dungeon_ID,
    name: rawMonster.Name,
    level: rawMonster.Stats.Level,
    isABoss: rawMonster.IsABoss,
    
    // Full stats for comprehensive monster data
    hp: rawMonster.Stats.HP,
    hpRecharge: rawMonster.Stats["HP Recharge"],
    attackRate: rawMonster.Stats["Attack Rate"],
    defenseRate: rawMonster.Stats["Defense Rate"],
    defense: rawMonster.Stats.Defense,
    defaultSkillPhysicalAttackMin: rawMonster.Stats["Default Skill Physical Attack Min"],
    defaultSkillPhysicalAttackMax: rawMonster.Stats["Default Skill Physical Attack Max"],
    defaultSkillReach: rawMonster.Stats["Default Skill Reach"],
    defaultSkillRange: rawMonster.Stats["Default Skill Range"],
    defaultSkillInterval: rawMonster.Stats["Default Skill Interval"],
    specialSkillPhysicalAttackMin: rawMonster.Stats["Special Skill Physical Attack Min"],
    specialSkillPhysicalAttackMax: rawMonster.Stats["Special Skill Physical Attack Max"],
    specialSkillReach: rawMonster.Stats["Special Skill Reach"],
    specialSkillRange: rawMonster.Stats["Special Skill Range"],
    specialSkillInterval: rawMonster.Stats["Special Skill Interval"],
    chaseRange: rawMonster.Stats["Chase Range"],
    exp: rawMonster.Stats.Exp,
    defaultSkillStance: rawMonster.Stats["Default Skill Stance"],
    specialSkillStance: rawMonster.Stats["Special Skill Stance"],
    specialSkillGroup: rawMonster.Stats["Special Skill Group"],
    damageReduction: rawMonster.Stats["Damage Reduction"],
    accuracy: rawMonster.Stats.Accuracy,
    penetration: rawMonster.Stats.Penetration,
    resistCriticalRate: rawMonster.Stats["Resist Critical Rate"],
    attackCountAmp: rawMonster.Stats["Attack Count Amp"],
    ignoreAccuracy: rawMonster.Stats["Ignore Accuracy"],
    ignoreDamageReduction: rawMonster.Stats["Ignore Damage Reduction"],
    ignorePenetration: rawMonster.Stats["Ignore Penetration"],
    absoluteDamage: rawMonster.Stats["Absolute Damage"],
    resistSkillAmp: rawMonster.Stats["Resist Skill Amp"],
    resistCriticalDamage: rawMonster.Stats["Resist Critical Damage"],
    resistSilence: rawMonster.Stats["Resist Silence"],
    hpDmgProp: rawMonster.Stats["HP Dmg Prop"],
    isWorldBoss: rawMonster.Stats["Is World Boss"],
  };

  // Cache the processed monster
  processedMonstersCache.set(cacheKey, processed);
  
  return processed;
}

/**
 * Build search index for efficient searching
 */
export function buildSearchIndex(rawMonsters: RawMonsterData[]): MonsterSearchIndex[] {
  if (isIndexBuilt && searchIndexCache.length > 0) {
    return searchIndexCache;
  }

  searchIndexCache.length = 0; // Clear existing cache
  
  for (let i = 0; i < rawMonsters.length; i++) {
    const rawMonster = rawMonsters[i];
    
    // Filter out monsters with empty names (like the original WordPress solution)
    if (!rawMonster.Name || rawMonster.Name.trim() === '') {
      continue;
    }
    
    const searchText = [
      rawMonster.Name,
      `Level ${rawMonster.Stats.Level}`,
      `Lv${rawMonster.Stats.Level}`,
      rawMonster.IsABoss ? 'boss' : 'normal'
    ].join(' ').toLowerCase();

    // Create unique ID consistent with processRawMonster
    const uniqueId = rawMonster.Dungeon_ID ? `${rawMonster.ID}-${rawMonster.Dungeon_ID}` : `${rawMonster.ID}-${i}`;

    searchIndexCache.push({
      id: uniqueId,
      name: rawMonster.Name,
      level: rawMonster.Stats.Level,
      isBoss: rawMonster.IsABoss,
      searchText
    });
  }

  isIndexBuilt = true;
  return searchIndexCache;
}

/**
 * Search monsters efficiently using the search index
 */
export function searchMonsters(
  rawMonsters: RawMonsterData[],
  searchTerm: string,
  filters: MonsterSearchFilters = {},
  limit: number = 50
): MonsterStats[] {
  const searchIndex = buildSearchIndex(rawMonsters);
  const normalizedSearch = searchTerm.toLowerCase().trim();
  
  let filteredIndex = searchIndex;

  // Apply text search
  if (normalizedSearch) {
    filteredIndex = searchIndex.filter(monster => 
      monster.searchText.includes(normalizedSearch)
    );
  }

  // Apply level filters
  if (filters.minLevel !== undefined) {
    filteredIndex = filteredIndex.filter(monster => monster.level >= filters.minLevel!);
  }
  if (filters.maxLevel !== undefined) {
    filteredIndex = filteredIndex.filter(monster => monster.level <= filters.maxLevel!);
  }

  // Apply boss filter
  if (filters.bossOnly) {
    filteredIndex = filteredIndex.filter(monster => monster.isBoss);
  }

  // Apply dungeon filter
  if (filters.dungeonId) {
    // We need to check the raw data for dungeon ID since it's not in the search index
    const dungeonFilteredIds = new Set(
      rawMonsters
        .filter(monster => monster.Dungeon_ID === filters.dungeonId)
        .map((monster, index) => 
          monster.Dungeon_ID ? `${monster.ID}-${monster.Dungeon_ID}` : `${monster.ID}-${index}`
        )
    );
    filteredIndex = filteredIndex.filter(monster => dungeonFilteredIds.has(monster.id));
  }

  // Limit results and process only what we need
  // Note: Sorting is now handled by the calling component for better control
  const limitedResults = filteredIndex.slice(0, limit);
  
  // Create a map with unique IDs for lookup
  const rawMonsterMap = new Map<string, { monster: RawMonsterData; index: number }>();
  rawMonsters.forEach((monster, index) => {
    const uniqueId = monster.Dungeon_ID ? `${monster.ID}-${monster.Dungeon_ID}` : `${monster.ID}-${index}`;
    rawMonsterMap.set(uniqueId, { monster, index });
  });
  
  return limitedResults
    .map(indexEntry => rawMonsterMap.get(indexEntry.id))
    .filter((entry): entry is { monster: RawMonsterData; index: number } => entry !== undefined)
    .map(entry => processRawMonster(entry.monster, entry.index));
}

/**
 * Get monster by ID efficiently
 */
export function getMonsterById(rawMonsters: RawMonsterData[], id: string): MonsterStats | null {
  // Check cache first using the unique cache key format
  const cacheKeys = [`${id}`, ...rawMonsters.map((_, i) => `${id}-${i}`)];
  for (const cacheKey of cacheKeys) {
    if (processedMonstersCache.has(cacheKey)) {
      return processedMonstersCache.get(cacheKey)!;
    }
  }

  // Find in raw data by unique ID
  for (let i = 0; i < rawMonsters.length; i++) {
    const rawMonster = rawMonsters[i];
    const uniqueId = rawMonster.Dungeon_ID ? `${rawMonster.ID}-${rawMonster.Dungeon_ID}` : `${rawMonster.ID}-${i}`;
    
    if (uniqueId === id) {
      return processRawMonster(rawMonster, i);
    }
  }

  return null;
}

/**
 * Get popular/recommended monsters (bosses, high level, etc.)
 */
export function getPopularMonsters(rawMonsters: RawMonsterData[], limit: number = 15): MonsterStats[] {
  return searchMonsters(rawMonsters, '', { bossOnly: true }, limit);
}

/**
 * Get all unique dungeon IDs from the monster data
 */
export function getDungeonIds(rawMonsters: RawMonsterData[]): string[] {
  const dungeonIds = new Set<string>();
  rawMonsters.forEach(monster => {
    // Filter out monsters with empty names (like the original WordPress solution)
    if (!monster.Name || monster.Name.trim() === '') {
      return;
    }
    
    if (monster.Dungeon_ID) {
      dungeonIds.add(monster.Dungeon_ID);
    }
  });
  return Array.from(dungeonIds).sort();
}

/**
 * Clear caches (useful for testing or memory management)
 */
export function clearCaches() {
  processedMonstersCache.clear();
  searchIndexCache.length = 0;
  isIndexBuilt = false;
}