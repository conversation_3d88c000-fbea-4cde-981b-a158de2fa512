'use client';

import { useState } from 'react';
import { ChevronDown, ChevronUp, Calendar, Code, Wrench, Sparkles } from 'lucide-react';

interface PatchItem {
  id: string;
  date: string;
  type: 'feature' | 'fix' | 'improvement' | 'tool';
  title: string;
  description: string;
  category?: string;
}

const RECENT_PATCHES: PatchItem[] = [
  {
    id: '2024-12-1',
    date: '2024-12-01',
    type: 'tool',
    title: 'Stats Wiki Implementation',
    description: 'Added comprehensive stats reference with interactive categories and search functionality.',
    category: 'New Tool'
  },
  {
    id: '2024-12-2',
    date: '2024-12-01',
    type: 'tool',
    title: 'Legal Information Page',
    description: 'Added GDPR-compliant legal information, privacy policy, and terms of use.',
    category: 'Legal'
  },
  {
    id: '2024-11-30',
    date: '2024-11-30',
    type: 'feature',
    title: 'Manual Stats Override Mode',
    description: 'Added toggle to use only manual stats for calculations, ignoring all other systems.',
    category: 'Build Planner'
  },
  {
    id: '2024-11-29',
    date: '2024-11-29',
    type: 'feature',
    title: 'Damage Analysis System',
    description: 'New comprehensive damage breakdown and analysis tool with detailed calculations.',
    category: 'Build Planner'
  },
  {
    id: '2024-11-28',
    date: '2024-11-28',
    type: 'improvement',
    title: 'Damage Calculator Improvements',
    description: 'Improved accuracy, removed PvP calculations, fixed variance minimum damage, and added monster selection.',
    category: 'Build Planner'
  },
  {
    id: '2024-11-27',
    date: '2024-11-27',
    type: 'feature',
    title: 'Force Wing System',
    description: 'New progression system added with stat bonuses and slot management.',
    category: 'Build Planner'
  },
  {
    id: '2024-11-26',
    date: '2024-11-26',
    type: 'improvement',
    title: 'Equipment System Overhaul',
    description: 'Unified upgrade modal layouts, improved UX, and fixed configuration isolation issues.',
    category: 'Build Planner'
  },
  {
    id: '2024-11-25',
    date: '2024-11-25',
    type: 'fix',
    title: 'Weapon System Fixes',
    description: 'Fixed 2-handed weapons tooltip, epic options not applying stats, and added missing Chakram materials.',
    category: 'Build Planner'
  }
];

const getTypeIcon = (type: PatchItem['type']) => {
  switch (type) {
    case 'feature':
      return <Sparkles className="w-4 h-4 text-green-400" />;
    case 'fix':
      return <Wrench className="w-4 h-4 text-blue-400" />;
    case 'improvement':
      return <Code className="w-4 h-4 text-yellow-400" />;
    case 'tool':
      return <Calendar className="w-4 h-4 text-purple-400" />;
    default:
      return <Calendar className="w-4 h-4 text-gray-400" />;
  }
};

const getTypeColor = (type: PatchItem['type']) => {
  switch (type) {
    case 'feature':
      return 'bg-green-500/10 text-green-400 border-green-500/20';
    case 'fix':
      return 'bg-blue-500/10 text-blue-400 border-blue-500/20';
    case 'improvement':
      return 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20';
    case 'tool':
      return 'bg-purple-500/10 text-purple-400 border-purple-500/20';
    default:
      return 'bg-gray-500/10 text-gray-400 border-gray-500/20';
  }
};

export default function RecentPatches() {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showAll, setShowAll] = useState(false);

  const displayedPatches = showAll ? RECENT_PATCHES : RECENT_PATCHES.slice(0, 3);

  return (
    <div className="bg-component-card border border-border-dark p-6 rounded-lg">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Calendar className="w-5 h-5 text-game-gold" />
          <h2 className="text-2xl font-semibold text-game-gold">Recent Updates</h2>
        </div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center gap-1 text-sm text-game-highlight hover:text-game-highlight/80 transition-colors"
        >
          {isExpanded ? (
            <>
              <span>Collapse</span>
              <ChevronUp className="w-4 h-4" />
            </>
          ) : (
            <>
              <span>Expand</span>
              <ChevronDown className="w-4 h-4" />
            </>
          )}
        </button>
      </div>

      <p className="text-foreground/80 mb-4">
        Latest features, improvements, and fixes across all tools.
      </p>

      {/* Compact View */}
      {!isExpanded && (
        <div className="space-y-2">
          {RECENT_PATCHES.slice(0, 2).map((patch) => (
            <div key={patch.id} className="flex items-center gap-3 p-2 rounded-lg bg-foreground/5">
              {getTypeIcon(patch.type)}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-medium text-white truncate">{patch.title}</span>
                  {patch.category && (
                    <span className={`px-2 py-0.5 text-xs rounded-full border ${getTypeColor(patch.type)}`}>
                      {patch.category}
                    </span>
                  )}
                </div>
                <p className="text-sm text-foreground/70 line-clamp-1">{patch.description}</p>
              </div>
              <span className="text-xs text-foreground/50 whitespace-nowrap">
                {new Date(patch.date).toLocaleDateString()}
              </span>
            </div>
          ))}
          <div className="text-center pt-2">
            <span className="text-sm text-foreground/60">
              +{RECENT_PATCHES.length - 2} more updates
            </span>
          </div>
        </div>
      )}

      {/* Expanded View */}
      {isExpanded && (
        <div className="space-y-3">
          {displayedPatches.map((patch) => (
            <div key={patch.id} className="border border-border-dark rounded-lg p-4 bg-foreground/5">
              <div className="flex items-start gap-3">
                {getTypeIcon(patch.type)}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="font-semibold text-white">{patch.title}</h3>
                    {patch.category && (
                      <span className={`px-2 py-1 text-xs rounded-full border ${getTypeColor(patch.type)}`}>
                        {patch.category}
                      </span>
                    )}
                  </div>
                  <p className="text-foreground/80 leading-relaxed">{patch.description}</p>
                </div>
                <div className="text-right">
                  <span className="text-xs text-foreground/50">
                    {new Date(patch.date).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          ))}

          {RECENT_PATCHES.length > 3 && (
            <div className="text-center pt-2">
              <button
                onClick={() => setShowAll(!showAll)}
                className="text-sm text-game-highlight hover:text-game-highlight/80 transition-colors"
              >
                {showAll ? 'Show Less' : `Show All ${RECENT_PATCHES.length} Updates`}
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}