// Pet System Data
// Contains all data definitions for the pet system including:
// - Available stats for each tier

import { PetStat, PetTierStats } from '../types/pet';

// Transcendence stats (Episode 39 content)
export const transcendenceStats: PetStat[] = [
  { id: 'critDamage', value: 5 },
  { id: 'allSkillAmp', value: 3 },
  { id: 'penetration', value: 17 },
  { id: 'allAttackUp', value: 27 },
  { id: 'cancelIgnorePenetration', value: 18 },
  { id: 'str', value: 20 },
  { id: 'dex', value: 20 },
  { id: 'int', value: 20 },
  { id: 'damageReduce', value: 18 },
  { id: 'defense', value: 35 },
  { id: 'addDamage', value: 40 },
  { id: 'normalDamageUp', value: 4 },
  { id: 'cancelIgnoreDamageReduce', value: 20 },
  { id: 'hp', value: 200 },
  { id: 'cancelIgnoreEvasion', value: 120 },
  { id: 'ignoreAccuracy', value: 140 },
  { id: 'evasion', value: 120 },
  { id: 'ignoreEvasion', value: 140 },
  { id: 'accuracy', value: 120 },
  { id: 'defenseRate', value: 90 },
  { id: 'attackRate', value: 140 },
  { id: 'ignoreResistKnockback', value: 3 },
  { id: 'ignoreResistStun', value: 3 },
  { id: 'ignoreResistDown', value: 3 },
  { id: 'resistKnockback', value: 2 },
  { id: 'resistStun', value: 2 },
  { id: 'resistDown', value: 2 }
];

export const petTierStats: PetTierStats = {
  normal: [
    { id: 'hp', value: 80 },
    { id: 'mp', value: 30 },
    { id: 'allAttackUp', value: 10 },
    { id: 'defense', value: 12 },
    { id: 'attackRate', value: 80 },
    { id: 'defenseRate', value: 45 },
    { id: 'critDamage', value: 2 },
    { id: 'critRate', value: 1 },
    { id: 'minDamage', value: 1 },
    { id: 'maxHpSteal', value: 5 },
    { id: 'maxMpSteal', value: 5 },
    { id: 'maxCritRate', value: 1 },
    { id: 'allSkillAmp', value: 1 },
    { id: 'hpAbsorb', value: 1 },
    { id: 'mpAbsorb', value: 1 },
    { id: 'evasion', value: 100 },
    { id: 'hpAutoHeal', value: 5 },
    { id: 'mpAutoHeal', value: 5 },
    { id: 'addDamage', value: 10 },
    { id: 'skillExp', value: 10 },
    { id: 'alzDropAmount', value: 10 },
    { id: '2SlotDropRate', value: 2 }, // Using the same ID as in stats-config.ts
    { id: 'resistCritRate', value: 1 },
    { id: 'resistCritDmg', value: 2 },
    { id: 'resistUnableToMove', value: 2 },
    { id: 'resistDown', value: 2 },
    { id: 'resistKnockback', value: 2 },
    { id: 'resistStun', value: 2 },
  ],
  covenant: [
    { id: 'hp', value: 120 },
    { id: 'mp', value: 50 },
    { id: 'allAttackUp', value: 15 },
    { id: 'defense', value: 20 },
    { id: 'attackRate', value: 100 },
    { id: 'defenseRate', value: 60 },
    { id: 'critDamage', value: 4 },
    { id: 'allSkillAmp', value: 2 },
    { id: 'hpAutoHeal', value: 10 },
    { id: 'mpAutoHeal', value: 10 },
    { id: 'resistCritDmg', value: 5 },
    { id: 'resistDown', value: 2 },
    { id: 'resistKnockback', value: 2 },
    { id: 'resistStun', value: 2 },
    { id: 'addDamage', value: 20 },
    { id: 'accuracy', value: 80 },
    { id: 'penetration', value: 11 },
    { id: 'ignorePenetration', value: 12 },
    { id: 'resistSkillAmp', value: 2 },
    { id: 'ignoreAccuracy', value: 100 },
    { id: 'damageReduce', value: 12 },
    { id: 'ignoreDamageReduce', value: 25 },
    { id: 'str', value: 10 },
    { id: 'int', value: 10 },
    { id: 'dex', value: 10 },
    { id: 'ignoreResistCritDmg', value: 6 },
    { id: 'ignoreResistSkillAmp', value: 3 },
  ],
  trust: [
    { id: 'hp', value: 160 },
    { id: 'allAttackUp', value: 20 },
    { id: 'defense', value: 30 },
    { id: 'attackRate', value: 120 },
    { id: 'defenseRate', value: 75 },
    { id: 'hpAutoHeal', value: 15 },
    { id: 'mpAutoHeal', value: 15 },
    { id: 'addDamage', value: 30 },
    { id: 'resistCritDmg', value: 8 },
    { id: 'resistDown', value: 2 },
    { id: 'resistKnockback', value: 2 },
    { id: 'resistStun', value: 2 },
    { id: 'accuracy', value: 100 },
    { id: 'penetration', value: 15 },
    { id: 'ignorePenetration', value: 16 },
    { id: 'resistSkillAmp', value: 3 },
    { id: 'ignoreAccuracy', value: 120 },
    { id: 'damageReduce', value: 15 },
    { id: 'ignoreDamageReduce', value: 35 },
    { id: 'ignoreResistCritDmg', value: 8 },
    { id: 'ignoreResistSkillAmp', value: 4 },
    { id: 'ignoreResistCritRate', value: 1 },
    { id: 'normalDamageUp', value: 3 },
    { id: 'ignoreResistKnockback', value: 3 },
    { id: 'ignoreResistDown', value: 3 },
    { id: 'ignoreResistStun', value: 3 },
    { id: 'auraDurationIncrease', value: 2 },
  ],
  transcendence: transcendenceStats
};