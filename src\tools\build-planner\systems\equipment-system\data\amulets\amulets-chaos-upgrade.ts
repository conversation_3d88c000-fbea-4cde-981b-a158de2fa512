/**
 * Amulets Chaos Upgrade Data
 * Contains the chaos upgrade bonuses for amulets (levels 0-15)
 * Based on the official Cabal Online amulets chaos upgrade table
 */

export interface AmuletChaosUpgradeStats {
  defense?: number;
  defenseRate?: number;
  ignoreAccuracy?: number;
  ignoreEvasion?: number;
  ignoreDamageReduce?: number;
  critDamage?: number; // Crit. DMG (percentage)
  resistCritDmg?: number; // Resist Crit. DMG (percentage)
  allSkillAmp?: number; // All Skill Amp (percentage)
  resistSkillAmp?: number; // Resist Skill Amp (percentage)
  maxCritRate?: number; // Max Crit. Rate (percentage)
}

export interface AmuletChaosUpgrade {
  level: number;
  stats: AmuletChaosUpgradeStats;
}

// Chaos upgrade levels (0-15) - shared by ALL amulets
// Data extracted from official Cabal Online amulets chaos upgrade table
export const amuletChaosUpgrades: AmuletChaosUpgrade[] = [
  { level: 0, stats: {} }, // No bonus at level 0
  { level: 1, stats: { defense: 10, defenseRate: 15 } },
  { level: 2, stats: { defense: 20, defenseRate: 30 } },
  { level: 3, stats: { defense: 30, defenseRate: 45, ignoreAccuracy: 10 } },
  { level: 4, stats: { defense: 40, defenseRate: 60, ignoreAccuracy: 20, ignoreEvasion: 10 } },
  { level: 5, stats: { defense: 50, defenseRate: 75, ignoreAccuracy: 30, ignoreEvasion: 20, ignoreDamageReduce: 5 } },
  { level: 6, stats: { defense: 60, defenseRate: 90, ignoreAccuracy: 40, ignoreEvasion: 30, ignoreDamageReduce: 10, critDamage: 1 } },
  { level: 7, stats: { defense: 70, defenseRate: 105, ignoreAccuracy: 50, ignoreEvasion: 40, ignoreDamageReduce: 15, critDamage: 2, resistCritDmg: 1 } },
  { level: 8, stats: { defense: 80, defenseRate: 120, ignoreAccuracy: 60, ignoreEvasion: 50, ignoreDamageReduce: 20, critDamage: 3, resistCritDmg: 2 } },
  { level: 9, stats: { defense: 90, defenseRate: 135, ignoreAccuracy: 70, ignoreEvasion: 60, ignoreDamageReduce: 25, critDamage: 4, resistCritDmg: 3 } },
  { level: 10, stats: { defense: 100, defenseRate: 150, ignoreAccuracy: 80, ignoreEvasion: 70, ignoreDamageReduce: 30, critDamage: 5, resistCritDmg: 4 } },
  { level: 11, stats: { defense: 110, defenseRate: 165, ignoreAccuracy: 90, ignoreEvasion: 80, ignoreDamageReduce: 35, critDamage: 6, resistCritDmg: 5, allSkillAmp: 1 } },
  { level: 12, stats: { defense: 120, defenseRate: 180, ignoreAccuracy: 100, ignoreEvasion: 90, ignoreDamageReduce: 40, critDamage: 7, resistCritDmg: 6, allSkillAmp: 2, resistSkillAmp: 1 } },
  { level: 13, stats: { defense: 130, defenseRate: 195, ignoreAccuracy: 110, ignoreEvasion: 100, ignoreDamageReduce: 45, critDamage: 8, resistCritDmg: 7, allSkillAmp: 3, resistSkillAmp: 2 } },
  { level: 14, stats: { defense: 140, defenseRate: 210, ignoreAccuracy: 120, ignoreEvasion: 110, ignoreDamageReduce: 50, critDamage: 9, resistCritDmg: 8, allSkillAmp: 4, resistSkillAmp: 3 } },
  { level: 15, stats: { defense: 150, defenseRate: 225, ignoreAccuracy: 130, ignoreEvasion: 120, ignoreDamageReduce: 50, critDamage: 10, resistCritDmg: 9, allSkillAmp: 5, resistSkillAmp: 4, maxCritRate: 1 } }
];

// Helper function to get chaos upgrade stats for a specific level
export const getAmuletChaosUpgradeStats = (level: number): AmuletChaosUpgradeStats => {
  if (level < 0 || level > 15) {
    return {};
  }
  return amuletChaosUpgrades[level]?.stats || {};
};

// Helper function to get the maximum chaos level for amulets
export const getMaxAmuletChaosLevel = (): number => {
  return 15;
};

// Helper function to validate chaos level
export const isValidAmuletChaosLevel = (level: number): boolean => {
  return level >= 0 && level <= 15;
};

// Export the chaos upgrade data for use in other modules
export default amuletChaosUpgrades;