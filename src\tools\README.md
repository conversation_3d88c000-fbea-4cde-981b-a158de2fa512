# Tools Directory Structure

This directory contains tool-specific components and data for each calculator.

## Structure

```
src/tools/
├── chloe-calculator/
│   ├── data/
│   │   └── recipes.ts          # Chloe crafting recipes (5 samples)
│   └── index.ts                # Main exports
├── devil-shop-calculator/
│   ├── data/
│   │   └── items.ts            # Devil shop items (5 samples)
│   └── index.ts                # Main exports
└── README.md                   # This file
```

## Shared Data

Shared game data (like the item registry) is located in:
- `src/lib/game-data/items.ts` - Central item registry
- `src/stores/priceStore.ts` - Price management store

## Usage

Each tool imports its own data and the shared components:

```typescript
// Chloe Calculator
import { CHLOE_RECIPES, getRecipesByCategory } from '@/tools/chloe-calculator';
import { GAME_ITEMS, getItemByName } from '@/lib/game-data';
import { usePriceStore } from '@/stores/priceStore';

// Devil Shop Calculator  
import { DEVIL_SHOP_ITEMS, getTokenCost } from '@/tools/devil-shop-calculator';
import { GAME_ITEMS } from '@/lib/game-data';
import { usePriceStore } from '@/stores/priceStore';
```

## Data Structure

- **Sample Data**: Each tool contains 5 sample recipes/items for development
- **Type Safety**: Full TypeScript interfaces for all data
- **Consistent**: All tools reference the same item names from the central registry
- **Extensible**: Easy to add more recipes/items when needed