'use client';

import React, { useEffect } from 'react';
import { MonsterStats } from '../../../lib/game-data/monsters/types';

interface MonsterDetailsModalProps {
  monster: MonsterStats | null;
  isOpen: boolean;
  onClose: () => void;
}

export const MonsterDetailsModal: React.FC<MonsterDetailsModalProps> = ({
  monster,
  isOpen,
  onClose
}) => {
  // Close modal on Escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen || !monster) return null;

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toLocaleString();
  };

  const formatValue = (key: string, value: any): string => {
    if (value === null || value === undefined) return 'N/A';
    if (typeof value === 'number') {
      // Don't format interval values as they're usually small decimals
      if (key.toLowerCase().includes('interval')) {
        return value.toString();
      }
      return formatNumber(value);
    }
    if (typeof value === 'boolean') {
      return value ? 'Yes' : 'No';
    }
    return value.toString();
  };

  // Group stats by category for better organization
  const statCategories = {
    'Basic Information': [
      { key: 'level', label: 'Level', value: monster.level },
      { key: 'hp', label: 'HP', value: monster.hp },
      { key: 'dungeonId', label: 'Dungeon ID', value: monster.dungeonId || 'N/A' },
      { key: 'isABoss', label: 'Is Boss', value: monster.isABoss },
      { key: 'isWorldBoss', label: 'Is World Boss', value: monster.isWorldBoss === 1 },
    ],
    'Combat Stats': [
      { key: 'attackRate', label: 'Attack Rate', value: monster.attackRate },
      { key: 'defenseRate', label: 'Defense Rate', value: monster.defenseRate },
      { key: 'defense', label: 'Defense', value: monster.defense },
      { key: 'accuracy', label: 'Accuracy', value: monster.accuracy },
      { key: 'penetration', label: 'Penetration', value: monster.penetration },
      { key: 'damageReduction', label: 'Damage Reduction', value: monster.damageReduction },
      { key: 'attackCountAmp', label: 'Attack Count Amp', value: monster.attackCountAmp },
    ],
    'Default Skill Stats': [
      { key: 'defaultSkillPhysicalAttackMin', label: 'Physical Attack Min', value: monster.defaultSkillPhysicalAttackMin },
      { key: 'defaultSkillPhysicalAttackMax', label: 'Physical Attack Max', value: monster.defaultSkillPhysicalAttackMax },
      { key: 'defaultSkillReach', label: 'Reach', value: monster.defaultSkillReach },
      { key: 'defaultSkillRange', label: 'Range', value: monster.defaultSkillRange },
      { key: 'defaultSkillInterval', label: 'Interval', value: monster.defaultSkillInterval },
      { key: 'defaultSkillStance', label: 'Stance', value: monster.defaultSkillStance },
    ],
    'Special Skill Stats': [
      { key: 'specialSkillPhysicalAttackMin', label: 'Physical Attack Min', value: monster.specialSkillPhysicalAttackMin },
      { key: 'specialSkillPhysicalAttackMax', label: 'Physical Attack Max', value: monster.specialSkillPhysicalAttackMax },
      { key: 'specialSkillReach', label: 'Reach', value: monster.specialSkillReach },
      { key: 'specialSkillRange', label: 'Range', value: monster.specialSkillRange },
      { key: 'specialSkillInterval', label: 'Interval', value: monster.specialSkillInterval },
      { key: 'specialSkillStance', label: 'Stance', value: monster.specialSkillStance },
      { key: 'specialSkillGroup', label: 'Group', value: monster.specialSkillGroup },
    ],
    'Resistance Stats': [
      { key: 'resistCriticalRate', label: 'Resist Critical Rate', value: monster.resistCriticalRate },
      { key: 'resistCriticalDamage', label: 'Resist Critical Damage', value: monster.resistCriticalDamage },
      { key: 'resistSkillAmp', label: 'Resist Skill Amp', value: monster.resistSkillAmp },
      { key: 'resistSilence', label: 'Resist Silence', value: monster.resistSilence },
    ],
    'Other Stats': [
      { key: 'hpRecharge', label: 'HP Recharge', value: monster.hpRecharge },
      { key: 'chaseRange', label: 'Chase Range', value: monster.chaseRange },
      { key: 'exp', label: 'EXP', value: monster.exp },
      { key: 'ignoreAccuracy', label: 'Ignore Accuracy', value: monster.ignoreAccuracy },
      { key: 'ignoreDamageReduction', label: 'Ignore Damage Reduction', value: monster.ignoreDamageReduction },
      { key: 'ignorePenetration', label: 'Ignore Penetration', value: monster.ignorePenetration },
      { key: 'absoluteDamage', label: 'Absolute Damage', value: monster.absoluteDamage },
      { key: 'hpDmgProp', label: 'HP Dmg Prop', value: monster.hpDmgProp },
    ],
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/70 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative w-full max-w-4xl max-h-[90vh] mx-4 glass-panel-dark border border-border-light rounded-lg shadow-game overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border-dark">
          <div>
            <h2 className="text-2xl font-bold text-game-gold glow-text-md">
              {monster.name}
              {monster.isABoss && (
                <span className="ml-3 px-3 py-1 bg-stat-offensive text-white text-sm rounded glow-text-sm">
                  BOSS
                </span>
              )}
            </h2>
            <p className="text-foreground/60 mt-1">Level {monster.level} Monster</p>
          </div>
          
          <button
            onClick={onClose}
            className="p-2 hover:bg-component-card rounded-lg transition-colors text-foreground/60 hover:text-foreground"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)] dark-scrollbar">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {Object.entries(statCategories).map(([categoryName, stats]) => (
              <div key={categoryName} className="component-bg">
                <h3 className="text-lg font-semibold text-game-gold glow-text-sm mb-4">
                  {categoryName}
                </h3>
                
                <div className="space-y-2">
                  {stats.map(({ key, label, value }) => (
                    <div key={key} className="flex justify-between items-center py-1">
                      <span className="text-foreground/80 text-sm">{label}:</span>
                      <span className="text-foreground font-medium">
                        {formatValue(key, value)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-border-dark">
          <button
            onClick={onClose}
            className="px-6 py-2 glass-panel border border-border-dark hover:border-border-light text-foreground rounded-md transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};