import React from 'react';
import { createPortal } from 'react-dom';
import { TieredBuff } from '../types';
import { useBuffsPotionsStore } from '../stores/buffs-potions-store';
import { formatStatValue } from '@/tools/build-planner/data/stats-config';

interface TierSelectorProps {
  buff: TieredBuff;
  activeTierId?: string;
  onClose: () => void;
}

const TierSelector: React.FC<TierSelectorProps> = ({ buff, activeTierId, onClose }) => {
  const { toggleBuff, selectBuffTier, isBuffActive } = useBuffsPotionsStore();

  const handleTierSelect = (tierId: string) => {
    if (isBuffActive(buff.id)) {
      // Update tier if buff is already active
      selectBuffTier(buff.id, tierId);
    } else {
      // Activate buff with selected tier
      toggleBuff(buff.id, tierId);
    }
    onClose();
  };

  return createPortal(
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/70 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal Content */}
      <div className="relative glass-panel-dark rounded-lg max-w-2xl w-full max-h-[85vh] overflow-y-auto dark-scrollbar m-4">
        <div className="flex justify-between items-center p-4 border-b" style={{ borderColor: 'var(--border-dark)' }}>
          <h3 className="text-xl font-bold text-game-gold">{buff.name} Levels</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            ✕
          </button>
        </div>

        <div className="p-4 space-y-2">
          {buff.tiers.map((tier) => {
            const isActiveTier = activeTierId === tier.id;
            
            return (
              <div
                key={tier.id}
                className={`
                  p-4 rounded cursor-pointer transition-all relative
                  ${isActiveTier ? 'bg-theme-light border-game-gold border' : 'bg-theme-darker hover:bg-theme-dark'}
                `}
                onClick={() => handleTierSelect(tier.id)}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="font-semibold text-game-gold text-base">
                    {tier.name}
                  </div>
                  {isActiveTier && (
                    <div className="text-xs px-2 py-1 bg-game-gold/20 text-game-gold rounded-full">
                      Active
                    </div>
                  )}
                </div>
                
                <div className="grid grid-cols-2 gap-x-4 gap-y-1 text-sm">
                  {Object.entries(tier.stats).map(([statId, value]) => {
                    const statOptions = useBuffsPotionsStore.getState().getAvailableStats(buff.id, tier.id);
                    const stat = statOptions.find(s => s.id === statId);
                    
                    if (!stat) return null;
                    
                    return (
                      <div
                        key={statId}
                        className="flex justify-between text-gray-300"
                      >
                        <span className="truncate pr-2">{stat.name}:</span>
                        <span className="font-medium">+{formatStatValue(statId, value)}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
        
        <div className="flex justify-end gap-3 p-3 border-t" style={{ borderColor: 'var(--border-dark)', backgroundColor: 'var(--theme-darkest)' }}>
          {isBuffActive(buff.id) && (
            <button
              onClick={() => {
                toggleBuff(buff.id); // This will deactivate the buff
                onClose();
              }}
              className="game-button bg-theme-darker text-red-400 hover:text-red-300 transition-colors"
            >
              Deactivate
            </button>
          )}
          <button
            onClick={onClose}
            className="game-button bg-theme-darker hover:bg-theme-lighter transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default TierSelector;