import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="game-header text-white border-t border-border-dark">
      <div className="container mx-auto max-w-8xl p-2 sm:p-4 md:p-5 lg:p-6">
        {/* Main Footer Content */}
        <div className="flex flex-col md:flex-row justify-end items-center md:items-start gap-8 md:gap-16 mb-6">
          {/* Quick Links */}
          <div className="text-center md:text-left">
            <h3 className="text-lg font-semibold text-game-gold mb-3">Tools</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/" className="text-gray-300 hover:text-white transition-colors">
                  Build Planner
                </Link>
              </li>
              <li>
                <Link href="/tier-lists" className="text-gray-300 hover:text-white transition-colors">
                  Tier Lists
                </Link>
              </li>
              <li>
                <Link href="/mob-table" className="text-gray-300 hover:text-white transition-colors">
                  Mob Table
                </Link>
              </li>
              <li>
                <Link href="/stats-wiki" className="text-gray-300 hover:text-white transition-colors">
                  Stats Wiki
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal & Contact */}
          <div className="text-center md:text-left">
            <h3 className="text-lg font-semibold text-game-gold mb-3">Legal & Contact</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/legal" className="text-gray-300 hover:text-white transition-colors">
                  Legal Information
                </Link>
              </li>
              <li>
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-gray-300 hover:text-white transition-colors"
                >
                  Contact Us
                </a>
              </li>
            </ul>
          </div>
        </div>

        {/* Copyright */}
        <div className="text-center border-t border-border-dark pt-4">
          <p className="text-sm text-gray-400" suppressHydrationWarning>
            &copy; {new Date().getFullYear()} Nipperlug. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}