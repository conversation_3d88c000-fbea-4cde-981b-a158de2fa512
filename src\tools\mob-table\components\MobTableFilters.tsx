'use client';

import React from 'react';
import { MonsterSearchFilters } from '../../../lib/game-data/monsters/types';

interface MobTableFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  filters: MonsterSearchFilters;
  onFiltersChange: (filters: MonsterSearchFilters) => void;
  dungeonIds: string[];
}

export const MobTableFilters: React.FC<MobTableFiltersProps> = ({
  searchTerm,
  onSearchChange,
  filters,
  onFiltersChange,
  dungeonIds
}) => {
  return (
    <div className="glass-panel p-6 mb-6">
      <h3 className="text-lg font-semibold text-game-gold glow-text-sm mb-4">Search & Filters</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Search Input */}
        <div>
          <label className="block text-sm font-medium text-foreground/80 mb-2">
            Search Monster Name
          </label>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            placeholder="Enter monster name..."
            className="w-full px-3 py-2 bg-component-card border border-border-dark rounded-md text-foreground placeholder-foreground/50 focus:outline-none focus:ring-2 focus:ring-stat-offensive focus:border-transparent"
          />
        </div>

        {/* Level Range */}
        <div>
          <label className="block text-sm font-medium text-foreground/80 mb-2">
            Min Level
          </label>
          <input
            type="number"
            value={filters.minLevel || ''}
            onChange={(e) => onFiltersChange({
              ...filters,
              minLevel: e.target.value ? parseInt(e.target.value) : undefined
            })}
            placeholder="Min level"
            className="w-full px-3 py-2 bg-component-card border border-border-dark rounded-md text-foreground placeholder-foreground/50 focus:outline-none focus:ring-2 focus:ring-stat-offensive focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground/80 mb-2">
            Max Level
          </label>
          <input
            type="number"
            value={filters.maxLevel || ''}
            onChange={(e) => onFiltersChange({
              ...filters,
              maxLevel: e.target.value ? parseInt(e.target.value) : undefined
            })}
            placeholder="Max level"
            className="w-full px-3 py-2 bg-component-card border border-border-dark rounded-md text-foreground placeholder-foreground/50 focus:outline-none focus:ring-2 focus:ring-stat-offensive focus:border-transparent"
          />
        </div>

        {/* Boss Filter */}
        <div>
          <label className="block text-sm font-medium text-foreground/80 mb-2">
            Monster Type
          </label>
          <select
            value={filters.bossOnly ? 'boss' : 'all'}
            onChange={(e) => onFiltersChange({
              ...filters,
              bossOnly: e.target.value === 'boss' ? true : undefined
            })}
            className="w-full px-3 py-2 bg-component-card border border-border-dark rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-stat-offensive focus:border-transparent"
          >
            <option value="all">All Monsters</option>
            <option value="boss">Bosses Only</option>
          </select>
        </div>

        {/* Dungeon Filter */}
        <div className="md:col-span-2 lg:col-span-4">
          <label className="block text-sm font-medium text-foreground/80 mb-2">
            Dungeon ID
          </label>
          <select
            value={filters.dungeonId || ''}
            onChange={(e) => onFiltersChange({
              ...filters,
              dungeonId: e.target.value || undefined
            })}
            className="w-full px-3 py-2 bg-component-card border border-border-dark rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-stat-offensive focus:border-transparent"
          >
            <option value="">All Dungeons</option>
            {dungeonIds.map(dungeonId => (
              <option key={dungeonId} value={dungeonId}>
                Dungeon {dungeonId}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Clear Filters Button */}
      <div className="mt-4">
        <button
          onClick={() => {
            onSearchChange('');
            onFiltersChange({});
          }}
          className="px-4 py-2 glass-panel border border-border-dark hover:border-border-light text-foreground rounded-md transition-colors"
        >
          Clear All Filters
        </button>
      </div>
    </div>
  );
};