/**
 * Shared armor types and interfaces
 * Contains all armor-related type definitions used across files
 */

export interface ArmorStats {
  defense?: number;
  defenseRate?: number;
  damageReduce?: number;
  hp?: number;
  allSkillAmp?: number;
  ignorePenetration?: number | null;
  resistCritDmg?: number;
  resistSkillAmp?: number;
}

export interface ArmorExtremeUpgradeLevel {
  defense?: number;
  defenseRate?: number;
  damageReduce?: number;
  allSkillAmp?: number;
  ignorePenetration?: number | null;
}

export interface ArmorDivineUpgradeLevel {
  defense?: number;
  defenseRate?: number;
  damageReduce?: number;
  resistCritDmg?: number;
  resistSkillAmp?: number;
  allSkillAmp?: number;
  hp?: number;
  allAttackUp?: number;
  accuracy?: number;
  resistCritRate?: number;
  ignoreAccuracy?: number;
  maxHpSteal?: number;
  ignorePenetration?: number;
}

export interface ArmorEpicOption {
  swordSkillAmp?: number;
  magicSkillAmp?: number;
  resistCritDmg?: number;
  resistSkillAmp?: number;
  damageReduce?: number;
  critDamage?: number;
  critRate?: number;
}

export interface ArmorSlotOption {
  swordSkillAmp: number;
  maxCritRate?: number;
  magicSkillAmp: number;
  critDamage?: number;
  critRate?: number;
}

export interface ArmorGradeData {
  baseStats: ArmorStats;
  maxExtremeLevel: number;
  imagePath: string;
  description?: string;
}

export interface ArmorTemplate {
  type: string; // 'armor'
  subtype: string; // 'body', 'helmet', 'gauntlet', 'shoes'
  weight?: 'light' | 'medium' | 'heavy'; // Only for body armor
  material: string;
  class: string;
  maxSlots: number;
  grades: Record<string, ArmorGradeData>;
}

export interface Armor {
  id: string;
  name: string;
  type: string; // 'armor'
  subtype: string; // 'body', 'helmet', 'gauntlet', 'shoes'
  weight?: 'light' | 'medium' | 'heavy'; // Only for body armor
  material: string;
  class: string;
  grade: string;
  imagePath: string;
  description?: string;
  baseStats: ArmorStats;
  maxSlots: number;
  maxExtremeLevel: number;
}

// Type identifiers for different armor subtypes
export type ArmorStatType = 'body' | 'helmet' | 'gauntlet' | 'shoes';