import Link from 'next/link';

export default function ChloeCalculatorsPage() {
  return (
    <div className="min-h-screen text-foreground p-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <Link href="/" className="text-blue-400 hover:text-blue-300 text-sm">
            ← Back to Home
          </Link>
          <h1 className="text-4xl font-bold mt-4 mb-4">Chloe Calculators</h1>
          <p className="text-foreground/80">
            Profit calculators for various crafting and trading systems.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Chloe Craft */}
          <div className="bg-component-card border border-border-dark p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-3">Chloe Craft</h2>
            <p className="text-foreground/80 mb-4 text-sm">
              Calculate profit margins for <PERSON> crafting recipes and materials.
            </p>
            <Link 
              href="/chloe-calculators/chloe-craft" 
              className="inline-block bg-pink-600 hover:bg-pink-700 px-4 py-2 rounded transition-colors text-sm"
            >
              Open Calculator
            </Link>
          </div>

          {/* Devil Shop */}
          <div className="bg-component-card border border-border-dark p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-3">Devil Shop</h2>
            <p className="text-foreground/80 mb-4 text-sm">
              Calculate profit margins for Devil Shop items and trading.
            </p>
            <Link 
              href="/chloe-calculators/devil-shop" 
              className="inline-block bg-red-600 hover:bg-red-700 px-4 py-2 rounded transition-colors text-sm"
            >
              Open Calculator
            </Link>
          </div>

          {/* Amity Craft */}
          <div className="bg-component-card border border-border-dark p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-3">Amity Craft</h2>
            <p className="text-foreground/80 mb-4 text-sm">
              Calculate profit margins for Amity crafting and materials.
            </p>
            <Link 
              href="/chloe-calculators/amity-craft" 
              className="inline-block bg-green-600 hover:bg-green-700 px-4 py-2 rounded transition-colors text-sm"
            >
              Open Calculator
            </Link>
          </div>
        </div>

        {/* Shared Price Management Info */}
        <div className="mt-8 bg-blue-900/30 border border-blue-500/50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">💡 Price Management</h3>
          <p className="text-foreground/80 text-sm">
            All Chloe calculators share the same price database. Update prices in one calculator 
            and they'll be available across all tools. Prices are automatically saved to your browser.
          </p>
        </div>
      </div>
    </div>
  );
}