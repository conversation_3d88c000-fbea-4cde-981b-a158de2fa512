import React from 'react';
import { ClassPassiveSkill } from '../types';
import { getStatInfo, formatStatValue } from '@/tools/build-planner/data/stats-config';

interface PassiveSkillSlotProps {
  skill: ClassPassiveSkill;
  currentLevel: number;
  onLevelChange: (skillId: string, level: number) => void;
}

export const PassiveSkillSlot: React.FC<PassiveSkillSlotProps> = ({
  skill,
  currentLevel,
  onLevelChange
}) => {
  const handleIncrement = () => {
    if (currentLevel < skill.maxLevel) {
      onLevelChange(skill.id, currentLevel + 1);
    }
  };

  const handleDecrement = () => {
    if (currentLevel > 0) {
      onLevelChange(skill.id, currentLevel - 1);
    }
  };

  const handleReset = () => {
    onLevelChange(skill.id, 0);
  };

  const handleMaxLevel = () => {
    onLevelChange(skill.id, skill.maxLevel);
  };

  // Get current level stats
  const getCurrentStats = () => {
    if (currentLevel <= 0 || !skill.levelStats) return {};
    
    const stats: Record<string, number> = {};
    Object.entries(skill.levelStats).forEach(([statId, values]) => {
      if (values[currentLevel - 1] !== undefined) {
        stats[statId] = values[currentLevel - 1];
      }
    });
    return stats;
  };

  const currentStats = getCurrentStats();
  const hasStats = Object.keys(currentStats).length > 0;

  return (
    <div className="bg-[#1b1b21b3] rounded-lg p-3 border border-[#2a2a3a] hover:border-game-gold/50 transition-all duration-300">
      {/* Skill Header */}
      <div className="flex items-center space-x-3 mb-3">
        <div className="w-10 h-10 bg-[#1a1a24] rounded border border-[#2a2a3a] flex items-center justify-center">
          <img
            src={skill.icon}
            alt={skill.name}
            className="w-8 h-8 object-contain"
            onError={(e) => {
              // Fallback to placeholder if image fails to load
              e.currentTarget.src = '/images/other icons/placeholder_skill.png';
            }}
          />
        </div>
        <div className="flex-1">
          <h4 className="text-white font-medium text-sm">{skill.name}</h4>
          <p className="text-gray-400 text-xs">{skill.description}</p>
        </div>
      </div>

      {/* Level Controls */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <span className="text-gray-300 text-xs">Level:</span>
          <span className="text-white font-semibold text-sm">
            {currentLevel}/{skill.maxLevel}
          </span>
        </div>
        
        <div className="flex items-center space-x-1">
          <button
            onClick={handleDecrement}
            disabled={currentLevel <= 0}
            className="bg-[#2a2a3a] hover:bg-[#3a3a4a] disabled:bg-[#1a1a2a] disabled:text-[#666] text-white px-2 py-1 rounded text-xs transition-colors border border-[#3a3a4a] disabled:border-[#2a2a2a] disabled:cursor-not-allowed"
          >
            -
          </button>
          <button
            onClick={handleIncrement}
            disabled={currentLevel >= skill.maxLevel}
            className="bg-[#2a2a3a] hover:bg-[#3a3a4a] disabled:bg-[#1a1a2a] disabled:text-[#666] text-white px-2 py-1 rounded text-xs transition-colors border border-[#3a3a4a] disabled:border-[#2a2a2a] disabled:cursor-not-allowed"
          >
            +
          </button>
          <button
            onClick={handleReset}
            disabled={currentLevel <= 0}
            className="bg-red-600/20 hover:bg-red-600/30 disabled:bg-[#1a1a2a] disabled:text-[#666] text-red-400 px-2 py-1 rounded text-xs transition-colors border border-red-600/30 disabled:border-[#2a2a2a] disabled:cursor-not-allowed"
          >
            Reset
          </button>
          <button
            onClick={handleMaxLevel}
            disabled={currentLevel >= skill.maxLevel}
            className="bg-game-gold/20 hover:bg-game-gold/30 disabled:bg-[#1a1a2a] disabled:text-[#666] text-game-gold px-2 py-1 rounded text-xs transition-colors border border-game-gold/30 disabled:border-[#2a2a2a] disabled:cursor-not-allowed"
          >
            Max
          </button>
        </div>
      </div>

      {/* Current Stats Display */}
      {hasStats && (
        <div className="space-y-1">
          <div className="text-xs text-gray-400 mb-1">Current Effects:</div>
          {Object.entries(currentStats).map(([statId, value]) => {
            const statInfo = getStatInfo(statId);
            const displayName = statInfo?.name || statId;
            const formattedValue = formatStatValue(statId, value);
            
            return (
              <div
                key={statId}
                className="flex justify-between items-center text-xs bg-[#1a1a24] rounded px-2 py-1"
              >
                <span className="text-gray-300">{displayName}</span>
                <span className="text-green-400">+{formattedValue}</span>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};