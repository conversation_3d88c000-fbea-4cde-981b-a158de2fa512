'use client';

import React from 'react';
import { MonsterStats } from '../../../lib/game-data/monsters/types';
import { TableColumn } from '../config/columns';

interface MobTableRowProps {
  monster: MonsterStats;
  visibleColumns: TableColumn[];
  onRowClick: (monster: MonsterStats) => void;
}

export const MobTableRow: React.FC<MobTableRowProps> = ({ monster, visibleColumns, onRowClick }) => {
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const getCellValue = (column: TableColumn): React.ReactNode => {
    const value = monster[column.key as keyof MonsterStats];
    
    switch (column.key) {
      case 'name':
        return (
          <span className={monster.isABoss ? "font-bold text-stat-offensive" : "font-medium"}>
            {monster.name}
          </span>
        );
      case 'level':
        return isNaN(monster.level) ? 'N/A' : monster.level;
      case 'hp':
        return formatNumber(monster.hp);
      case 'exp':
        return formatNumber(monster.exp);
      case 'dungeonId':
        return monster.dungeonId || 'N/A';
      case 'isWorldBoss':
        return monster.isWorldBoss === 1 ? 'Yes' : 'No';
      default:
        if (typeof value === 'number') {
          return formatNumber(value);
        }
        return value?.toString() || 'N/A';
    }
  };

  return (
    <tr 
      className="hover:bg-component-card/30 transition-colors cursor-pointer group"
      onClick={() => onRowClick(monster)}
    >
      {visibleColumns.map((column) => (
        <td 
          key={column.key} 
          className="px-2 py-2.5 text-foreground/80 text-sm group-hover:text-foreground transition-colors"
          style={{ width: column.width }}
          title={getCellValue(column)?.toString() || ''}
        >
          <div className={`truncate ${column.key === 'name' ? 'text-left' : 'text-center'}`}>
            {getCellValue(column)}
          </div>
        </td>
      ))}
    </tr>
  );
};