/**
 * Column configuration for the mob table
 * Defines which columns are available, their visibility, and sorting behavior
 */

export interface TableColumn {
  key: string;
  title: string;
  visible: boolean;
  required?: boolean;
  sortable?: boolean;
  width?: string;
  category: 'basic' | 'offensive' | 'defensive' | 'skills' | 'other';
}

export const COLUMN_CATEGORIES = {
  basic: 'Basic Info',
  offensive: 'Offensive Stats',
  defensive: 'Defensive Stats',
  skills: 'Skill Stats',
  other: 'Other Stats'
} as const;

export const DEFAULT_COLUMNS: TableColumn[] = [
  // Basic Info - Always visible
  { key: 'name', title: 'Name', visible: true, required: true, sortable: true, width: '250px', category: 'basic' },
  { key: 'level', title: 'Level', visible: true, required: true, sortable: true, width: '80px', category: 'basic' },
  { key: 'hp', title: 'HP', visible: true, sortable: true, width: '110px', category: 'basic' },
  
  // Offensive Stats
  { key: 'attackRate', title: 'Attack Rate', visible: false, sortable: true, width: '100px', category: 'offensive' },
  { key: 'accuracy', title: 'Accuracy', visible: false, sortable: true, width: '90px', category: 'offensive' },
  { key: 'penetration', title: 'Penetration', visible: false, sortable: true, width: '100px', category: 'offensive' },
  { key: 'attackCountAmp', title: 'Attack Count Amp', visible: false, sortable: true, width: '110px', category: 'offensive' },
  { key: 'ignoreAccuracy', title: 'Ignore Accuracy', visible: false, sortable: true, width: '100px', category: 'offensive' },
  { key: 'ignoreDamageReduction', title: 'Ignore DMG Reduction', visible: false, sortable: true, width: '120px', category: 'offensive' },
  { key: 'ignorePenetration', title: 'Ignore Penetration', visible: true, sortable: true, width: '100px', category: 'offensive' },
  { key: 'absoluteDamage', title: 'Absolute DMG', visible: false, sortable: true, width: '100px', category: 'offensive' },
  
  // Defensive Stats
  { key: 'defenseRate', title: 'Defense Rate', visible: false, sortable: true, width: '100px', category: 'defensive' },
  { key: 'defense', title: 'Defense', visible: true, sortable: true, width: '90px', category: 'defensive' },
  { key: 'damageReduction', title: 'DMG Reduction', visible: true, sortable: true, width: '100px', category: 'defensive' },
  { key: 'resistCriticalRate', title: 'Resist Crit Rate', visible: true, sortable: true, width: '120px', category: 'defensive' },
  { key: 'resistSkillAmp', title: 'Resist Skill Amp', visible: true, sortable: true, width: '120px', category: 'defensive' },
  { key: 'resistCriticalDamage', title: 'Resist Crit DMG', visible: true, sortable: true, width: '120px', category: 'defensive' },
  { key: 'resistSilence', title: 'Resist Silence', visible: false, sortable: true, width: '110px', category: 'defensive' },
  
  // Skill Stats
  { key: 'defaultSkillPhysicalAttackMin', title: 'Default Min ATK', visible: false, sortable: true, width: '120px', category: 'skills' },
  { key: 'defaultSkillPhysicalAttackMax', title: 'Default Max ATK', visible: false, sortable: true, width: '120px', category: 'skills' },
  { key: 'defaultSkillReach', title: 'Default Reach', visible: false, sortable: true, width: '110px', category: 'skills' },
  { key: 'defaultSkillRange', title: 'Default Range', visible: false, sortable: true, width: '110px', category: 'skills' },
  { key: 'defaultSkillInterval', title: 'Default Interval', visible: false, sortable: true, width: '120px', category: 'skills' },
  { key: 'specialSkillPhysicalAttackMin', title: 'Special Min ATK', visible: false, sortable: true, width: '120px', category: 'skills' },
  { key: 'specialSkillPhysicalAttackMax', title: 'Special Max ATK', visible: false, sortable: true, width: '120px', category: 'skills' },
  { key: 'specialSkillReach', title: 'Special Reach', visible: false, sortable: true, width: '110px', category: 'skills' },
  { key: 'specialSkillRange', title: 'Special Range', visible: false, sortable: true, width: '110px', category: 'skills' },
  { key: 'specialSkillInterval', title: 'Special Interval', visible: false, sortable: true, width: '120px', category: 'skills' },
  { key: 'defaultSkillStance', title: 'Default Stance', visible: false, sortable: true, width: '110px', category: 'skills' },
  { key: 'specialSkillStance', title: 'Special Stance', visible: false, sortable: true, width: '110px', category: 'skills' },
  { key: 'specialSkillGroup', title: 'Special Group', visible: false, sortable: true, width: '110px', category: 'skills' },
  
  // Other Stats
  { key: 'hpRecharge', title: 'HP Recharge', visible: false, sortable: true, width: '100px', category: 'other' },
  { key: 'chaseRange', title: 'Chase Range', visible: false, sortable: true, width: '110px', category: 'other' },
  { key: 'exp', title: 'EXP', visible: false, sortable: true, width: '100px', category: 'other' },
  { key: 'hpDmgProp', title: 'HP Dmg Prop', visible: false, sortable: true, width: '110px', category: 'other' },
  { key: 'isWorldBoss', title: 'Is World Boss', visible: false, sortable: true, width: '100px', category: 'other' },
  { key: 'dungeonId', title: 'Dungeon ID', visible: true, sortable: true, width: '100px', category: 'other' },
];

export type SortDirection = 'asc' | 'desc' | null;

export interface SortConfig {
  key: string;
  direction: SortDirection;
}