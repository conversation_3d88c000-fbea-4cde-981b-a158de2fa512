'use client';

import React, { useState, useMemo } from 'react';
import Link from 'next/link';
import { getStatsByCategory, getStatInfo, statsConfig } from '../build-planner/data/stats-config';
import { StatIconClient } from './components/StatIconClient';

type StatCategory = 'offensive' | 'defensive' | 'utility';

const CATEGORY_INFO = {
  offensive: {
    title: 'Attack Stats',
    description: 'These stats determine your offensive capabilities, affecting how much damage you deal to enemies and your effectiveness in combat.',
    color: 'stat-offensive'
  },
  defensive: {
    title: 'Defense Stats', 
    description: 'These stats determine your defensive capabilities, affecting how much damage you take from enemies and your survivability in combat.',
    color: 'stat-defensive'
  },
  utility: {
    title: 'Other Stats',
    description: 'These stats cover various utility and survival aspects, affecting your character\'s sustainability, mobility, and resistance to crowd control effects.',
    color: 'stat-utility'
  }
} as const;

// Custom function for Stats Wiki to order stats: base -> pvp -> pve
function getStatsByCategoryForWiki(category: 'offensive' | 'defensive' | 'utility'): string[] {
  const baseStats: string[] = [];
  const pvpStats: string[] = [];
  const pveStats: string[] = [];

  // Collect all base stats in this category
  for (const statId in statsConfig.stats) {
    if (statsConfig.stats[statId].category === category) {
      baseStats.push(statId);
      
      // Check for variants
      if (statsConfig.stats[statId].variants) {
        statsConfig.stats[statId].variants.forEach(variant => {
          const variantStatId = variant + statId.charAt(0).toUpperCase() + statId.slice(1);
          if (variant === 'pvp') {
            pvpStats.push(variantStatId);
          } else if (variant === 'pve') {
            pveStats.push(variantStatId);
          }
        });
      }
    }
  }

  // Return in order: base stats, then all pvp stats, then all pve stats
  return [...baseStats, ...pvpStats, ...pveStats];
}

export default function StatsWiki() {
  const [activeCategory, setActiveCategory] = useState<StatCategory>('offensive');
  const [searchTerm, setSearchTerm] = useState('');

  // Get stats for the active category using custom Wiki ordering
  const categoryStats = useMemo(() => {
    const statIds = getStatsByCategoryForWiki(activeCategory);
    return statIds
      .map(statId => ({ id: statId, info: getStatInfo(statId) }))
      .filter(stat => stat.info !== null);
  }, [activeCategory]);

  // Filter stats based on search term
  const filteredStats = useMemo(() => {
    if (!searchTerm) return categoryStats;
    
    const term = searchTerm.toLowerCase();
    return categoryStats.filter(stat => 
      stat.info!.name.toLowerCase().includes(term) ||
      stat.info!.description.toLowerCase().includes(term)
    );
  }, [categoryStats, searchTerm]);

  const categoryInfo = CATEGORY_INFO[activeCategory];

  return (
    <div className="min-h-screen text-foreground p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <Link href="/" className="text-blue-400 hover:text-blue-300 text-sm">
            ← Back to Home
          </Link>
          <h1 className="text-4xl font-bold mt-4 mb-4">Stats Wiki</h1>
          <p className="text-foreground/80">
            Comprehensive reference for game statistics and mechanics.
          </p>
        </div>

        {/* Search Bar */}
        <div className="mb-6">
          <input
            type="search"
            placeholder="Search for a stat..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-3 bg-component-card border border-border-dark rounded-lg text-foreground placeholder-foreground/50 focus:outline-none focus:border-stat-offensive transition-colors"
          />
        </div>

        {/* Category Tabs */}
        <div className="flex mb-8 bg-component-card border border-border-dark rounded-lg overflow-hidden">
          {(Object.keys(CATEGORY_INFO) as StatCategory[]).map((category) => {
            const info = CATEGORY_INFO[category];
            const isActive = activeCategory === category;
            
            return (
              <button
                key={category}
                onClick={() => setActiveCategory(category)}
                className={`flex-1 px-6 py-4 font-semibold transition-all ${
                  isActive
                    ? category === 'offensive'
                      ? 'text-stat-offensive bg-stat-offensive-bg border-b-2 border-stat-offensive'
                      : category === 'defensive'
                      ? 'text-stat-defensive bg-stat-defensive-bg border-b-2 border-stat-defensive'
                      : 'text-stat-utility bg-stat-utility-bg border-b-2 border-stat-utility'
                    : 'text-foreground/70 hover:text-foreground hover:bg-foreground/5'
                }`}
              >
                {info.title}
              </button>
            );
          })}
        </div>

        {/* Category Description */}
        <div className={`mb-8 p-4 rounded-r-lg ${
          activeCategory === 'offensive' 
            ? 'bg-stat-offensive-bg border-l-4 border-stat-offensive' 
            : activeCategory === 'defensive'
            ? 'bg-stat-defensive-bg border-l-4 border-stat-defensive'
            : 'bg-stat-utility-bg border-l-4 border-stat-utility'
        }`}>
          <h3 className={`text-xl font-semibold mb-2 ${
            activeCategory === 'offensive' 
              ? 'text-stat-offensive' 
              : activeCategory === 'defensive'
              ? 'text-stat-defensive'
              : 'text-stat-utility'
          }`}>
            {categoryInfo.title}
          </h3>
          <p className="text-foreground/80">
            {categoryInfo.description}
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredStats.map((stat, index) => (
            <div
              key={stat.id}
              className={`bg-component-card border border-border-dark rounded-lg p-6 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 ${
                activeCategory === 'offensive' 
                  ? 'hover:border-stat-offensive hover:shadow-stat-offensive/10' 
                  : activeCategory === 'defensive'
                  ? 'hover:border-stat-defensive hover:shadow-stat-defensive/10'
                  : 'hover:border-stat-utility hover:shadow-stat-utility/10'
              }`}
              style={{ animationDelay: `${index * 50}ms` }}
            >
              {/* Stat Header */}
              <div className="flex items-center mb-4">
                <div className="mr-3 flex-shrink-0">
                  <StatIconClient 
                    statId={stat.id}
                    fallbackColor={categoryInfo.color}
                  />
                </div>
                <h3 className={`text-lg font-semibold ${
                  activeCategory === 'offensive' 
                    ? 'text-stat-offensive' 
                    : activeCategory === 'defensive'
                    ? 'text-stat-defensive'
                    : 'text-stat-utility'
                }`}>
                  {stat.info!.name}
                </h3>
              </div>

              {/* Description */}
              <div className="text-foreground/90 leading-relaxed">
                {stat.info!.description}
              </div>
            </div>
          ))}
        </div>

        {/* No Results */}
        {filteredStats.length === 0 && searchTerm && (
          <div className="text-center py-12">
            <p className="text-foreground/60 text-lg">
              No stats found matching "{searchTerm}"
            </p>
            <button
              onClick={() => setSearchTerm('')}
              className="mt-4 px-4 py-2 bg-stat-offensive text-white rounded-lg hover:bg-stat-offensive/80 transition-colors"
            >
              Clear Search
            </button>
          </div>
        )}


      </div>
    </div>
  );
}