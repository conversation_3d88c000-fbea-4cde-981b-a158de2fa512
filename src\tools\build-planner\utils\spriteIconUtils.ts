interface SpriteFrame {
  frame: { x: number; y: number; w: number; h: number };
}

interface SpriteData {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface SpritesheetMeta {
  size: { w: number; h: number };
}

let spriteFrames: Record<string, SpriteFrame> | null = null;
let spritesheetMeta: SpritesheetMeta | null = null;

export async function loadSpriteData(): Promise<Record<string, SpriteFrame>> {
  if (!spriteFrames && typeof window !== 'undefined') {
    const response = await fetch('/spritesheet-stat-icons.json');
    const data = await response.json();
    spriteFrames = data.frames;
    spritesheetMeta = data.meta;
  }
  return spriteFrames || {}; // Return empty object if not loaded (server-side)
}

export function getSpritesheetDimensions(): { width: number; height: number } {
  if (spritesheetMeta) {
    return { width: spritesheetMeta.size.w, height: spritesheetMeta.size.h };
  }
  // Fallback dimensions if meta data isn't loaded yet
  return { width: 4234, height: 42 };
}

export function getSpriteData(iconPath: string): SpriteData | null {
  if (!iconPath || !spriteFrames) return null;
  
  // Extract filename: "/images/stat icons/attack_icon.png" -> "attack_icon.png"
  const filename = iconPath.split('/').pop();
  
  if (!filename || !spriteFrames[filename]) {
    return null;
  }
  
  const frame = spriteFrames[filename].frame;
  return {
    x: frame.x,
    y: frame.y,
    width: frame.w,
    height: frame.h
  };
}

// Initialize sprite data on client-side only
if (typeof window !== 'undefined') {
  loadSpriteData();
}

