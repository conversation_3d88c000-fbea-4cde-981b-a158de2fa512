import Link from 'next/link';
import ForceWingCalculator from '@/tools/exp-calculators/ForceWingCalculator';

export default function ForceWingExpCalculatorPage() {
  return (
    <div className="min-h-screen text-foreground p-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <Link href="/exp-calculators" className="text-blue-400 hover:text-blue-300 text-sm">
            ← Back to EXP Calculators
          </Link>
          <h1 className="text-4xl font-bold mt-4 mb-4">Force Wing EXP Calculator</h1>
          <p className="text-foreground/80">
            Calculate experience needed for force wing upgrades and levels.
          </p>
        </div>
        
        <ForceWingCalculator />
      </div>
    </div>
  );
}