# Website Migration Plan: WordPress to Next.js

## Current Situation

### Existing Setup
- **Build Planner**: Next.js app (current main app)
  - Advanced character build optimization tool
  - 16+ game systems integration
  - Modern React/TypeScript codebase
- **WordPress Site**: Multiple custom calculators/tools as plugins
  - Tier lists (simple HTML with collapsible sections)
  - Stats wiki (single HTML page with tabs)
  - Mob table (JSON data + filtering)
  - EXP calculators (3 simple calculators with data tables)
  - Chloe craft calculators (3 calculators sharing localStorage prices)

### Migration Goal
Consolidate everything into single Next.js app with clean URLs and proper folder structure for multiple tools while preserving existing build planner functionality.

## Target Folder Structure

```
src/
├── app/
│   ├── page.tsx                    # Build planner (existing, no changes)
│   ├── mob-table/page.tsx          # Route to mob table (/mob-table)
│   ├── exp-calculators/            # EXP calculation routes
│   │   ├── page.tsx                # Overview of 3 calculators (/exp-calculators)
│   │   ├── character/page.tsx      # Character EXP calculator (/exp-calculators/character)
│   │   ├── force-wing/page.tsx     # Force Wing calculator (/exp-calculators/force-wing)
│   │   └── oxp/page.tsx            # OXP calculator (/exp-calculators/oxp)
│   ├── chloe-calculators/          # Chloe craft routes
│   │   ├── page.tsx                # Overview of 3 calculators (/chloe-calculators)
│   │   ├── chloe-craft/page.tsx    # Chloe craft calculator (/chloe-calculators/chloe-craft)
│   │   ├── devil-shop/page.tsx     # Devil shop calculator (/chloe-calculators/devil-shop)
│   │   └── amity-craft/page.tsx    # Amity craft calculator (/chloe-calculators/amity-craft)
│   ├── tier-lists/                 # Tier list routes
│   │   ├── page.tsx                # Overview (/tier-lists)
│   │   ├── single-target/page.tsx  # Single target tier list (/tier-lists/single-target)
│   │   ├── aoe/page.tsx            # AoE tier list (/tier-lists/aoe)
│   │   └── nation-war/page.tsx     # Nation war tier list (/tier-lists/nation-war)
│   └── stats-wiki/page.tsx         # Stats wiki route (/stats-wiki)
├── tools/                          # EXISTING: All tool logic stays here
│   ├── build-planner/             # Existing (no changes to structure)
│   │   └── [existing structure preserved]
│   ├── mob-table/                 # NEW: Monster stats table logic
│   │   ├── components/
│   │   └── MobTable.tsx
│   ├── exp-calculators/           # NEW: EXP calculation logic
│   │   ├── components/            # Shared EXP calculator components
│   │   ├── CharacterExpCalculator.tsx
│   │   ├── ForceWingCalculator.tsx
│   │   └── OxpCalculator.tsx
│   ├── chloe-calculators/         # NEW: Chloe craft logic
│   │   ├── components/            # Shared calculator components
│   │   ├── stores/                # Shared price management store
│   │   ├── ChloeCalculator.tsx
│   │   ├── DevilShopCalculator.tsx
│   │   └── AmityCalculator.tsx
│   ├── tier-lists/                # NEW: Tier list logic
│   │   ├── components/            # Tier list components
│   │   ├── SingleTargetTierList.tsx
│   │   ├── AoeTierList.tsx
│   │   └── NationWarTierList.tsx
│   └── stats-wiki/                # NEW: Stats wiki logic
│       ├── components/
│       └── StatsWiki.tsx
├── components/
│   ├── layout/                     # Site navigation, header, footer
│   └── ui/                         # Shared UI components (tables, buttons, etc)
├── lib/                            # Shared utilities and data
│   ├── game-data/                  # JSON data files and utilities
│   │   ├── monsters/              # Monster-related data
│   │   │   ├── mob-stats.json     # Shared monster stats JSON
│   │   │   ├── types.ts           # Shared monster types
│   │   │   └── monster-processor.ts # Shared monster processing logic
│   │   └── index.ts               # Export all shared data
│   └── hooks/                     # Global shared hooks
│       └── useMonsterData.ts      # Shared monster data hook
└── stores/                         # Global Zustand stores
    └── price-store.ts              # Shared price management for calculators
```

## Content to Migrate

### From WordPress Site
- **Tier Lists**: 3 HTML files (single-target, AoE, nation war) with collapsible sections
- **Stats Wiki**: Single HTML page with tabbed interface for game stats
- **Mob Table**: JSON data file + PHP filtering → React table with search/filter
- **EXP Calculators**: 3 separate calculators (character, force wing, OXP) with data tables
- **Chloe Calculators**: 3 profit calculators sharing localStorage price data

### Existing Build Planner
- Keep current structure under `/tools/build-planner/`
- Minimal changes to preserve functionality
- Remains accessible at root URL `/` for clean build planner access

## Migration Order (By Complexity)

### Phase 1: Folder Structure Setup
- Set up new folder structure with clean URLs directly under `/app/`
- Keep build planner logic under `/tools/build-planner/` (preserve existing structure)
- Create shared components and layout structure

### Phase 2: Simple Static Content ✅ COMPLETED
- ✅ Tier lists (convert HTML to React components) - **COMPLETED**
- ✅ Stats wiki (single page with tabs) - **COMPLETED**

### Phase 3: Data-Driven Tools ✅ COMPLETED
- ✅ Mob table (JSON + filtering) - **COMPLETED**
  - ✅ Global data architecture implemented
  - ✅ Shared monster data between build planner and mob table
  - ✅ Full monster database with search and filtering
  - ✅ Fixed HP display (removed duplicate Display HP column)
  - ✅ Fixed sorting to consider all 4,422 monsters in database
- ✅ EXP calculators (3 simple calculators) - **COMPLETED**

### Phase 4: Complex Calculators 🚧 IN PROGRESS
- ✅ Shared price store setup - **COMPLETED**
  - ✅ Global price store with API-ready architecture
  - ✅ Source tracking (user/API/NPC prices)
  - ✅ localStorage persistence with versioning
- ✅ Chloe Craft Calculator - **COMPLETED**
  - ✅ 164 recipes with full data migration
  - ✅ Advanced filtering, sorting, favorites
  - ✅ Batch calculation modal
  - ✅ Import/export functionality
  - ✅ Comprehensive help documentation
- ✅ Devil Shop Calculator - **COMPLETED**
  - Complete 24-item database with token-based profit calculations
  - Advanced filtering, sorting, and favorites system
  - Import price functionality and global price store integration
- 📋 Amity Calculator - **PLANNED**

## Key Architecture Decisions

### Clean URL Structure
- Tools are accessible directly without `/tools/` prefix for better UX
- Examples:
  - `/mob-table` instead of `/tools/mob-table`
  - `/exp-calculators/character` instead of `/tools/exp-calculators/character`
  - `/tier-lists/single-target` instead of `/tools/tier-lists/single-target`
- Build planner remains at root `/` for primary tool access

### Shared Price Store
- Zustand store for price management across all Chloe calculators
- localStorage persistence (matching current WordPress behavior)
- Shared between chloe-craft, devil-shop, and amity-craft calculators

### Component Organization
- Tool-specific components stay within each tool's folder
- Shared UI components (tables, buttons) in `/components/ui/`
- Each tool is self-contained and can be worked on independently

### Data Management
- JSON files in `/lib/game-data/` for static game data
- Client-side filtering and search for reference tables
- TypeScript interfaces for type safety

The migration will be executed in phases to minimize risk while maximizing the benefits of the new architecture.