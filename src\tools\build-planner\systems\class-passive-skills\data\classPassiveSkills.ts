// Class-specific Passive Skills Data
import type { CharacterClass } from '../../class/types';
import type { ClassPassiveSkill } from '../types';

// Main passive skills data structure
export const CLASS_PASSIVE_SKILLS: Record<CharacterClass, ClassPassiveSkill[]> = {
  blader: [],
  wizard: [
    {
      id: 'magic_control',
      name: 'Magic Control',
      description: '',
      icon: '/images/classes/passives/wizard/magic_control.png',
      stats: { magicAttack: 210 }, // Max level stats
      maxLevel: 20,
      levelStats: {
        // Magic Control: starts at 58, max at 210 (level 20)
        // Linear progression: (210 - 58) / 19 = 8 per level after level 1
        magicAttack: [
          58, 66, 74, 82, 90, 98, 106, 114, 122, 130,  // levels 1-10
          138, 146, 154, 162, 170, 178, 186, 194, 202, 210  // levels 11-20
        ]
      }
    },
    {
      id: 'piercing_spell',
      name: 'Piercing Spell',
      description: '',
      icon: '/images/passive-skills/wizard/piercing_spell.png',
      stats: { penetration: 80 }, // Max level stats
      maxLevel: 20,
      levelStats: {
        // Piercing Spell: starts at 23, max at 80 (level 20)
        // Linear progression: (80 - 23) / 19 = 3 per level after level 1
        penetration: [
          23, 26, 29, 32, 35, 38, 41, 44, 47, 50,  // levels 1-10
          53, 56, 59, 62, 65, 68, 71, 74, 77, 80   // levels 11-20
        ]
      }
    },
    {
      id: 'focus',
      name: 'Focus',
      description: '',
      icon: '/images/passive-skills/wizard/focus.png',
      stats: { attackRate: 400, defenseRate: 300, pveDamageReduce: 10 }, // Max level stats
      maxLevel: 20,
      levelStats: {
        // Focus: Linear scaling to max values at level 20
        // Attack Rate: 400 / 20 = 20 per level
        attackRate: [
          20, 40, 60, 80, 100, 120, 140, 160, 180, 200,  // levels 1-10
          220, 240, 260, 280, 300, 320, 340, 360, 380, 400  // levels 11-20
        ],
        // Defense Rate: 300 / 20 = 15 per level
        defenseRate: [
          15, 30, 45, 60, 75, 90, 105, 120, 135, 150,  // levels 1-10
          165, 180, 195, 210, 225, 240, 255, 270, 285, 300  // levels 11-20
        ],
        // PvE Damage Reduce: 10 / 20 = 0.5 per level
        pveDamageReduce: [
          0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5,  // levels 1-10
          5.5, 6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10  // levels 11-20
        ]
      }
    },
    {
      id: 'wizard_mastery',
      name: 'Wizard Mastery',
      description: '',
      icon: '/images/passive-skills/wizard/wizard_mastery.png',
      stats: { hp: 70, pvpResistMagicSkillAmp: 9, pvpResistSwordSkillAmp: 2 }, // Only level 1
      maxLevel: 1,
      levelStats: {
        hp: [70],
        pvpResistMagicSkillAmp: [9],
        pvpResistSwordSkillAmp: [2]
      }
    }
  ],
  warrior: [], // TODO: Add warrior passive skills
  gladiator: [], // TODO: Add gladiator passive skills
  dark_mage: [], // TODO: Add dark mage passive skills
  force_archer: [], // TODO: Add force archer passive skills
  force_gunner: [], // TODO: Add force gunner passive skills
  force_blader: [], // TODO: Add force blader passive skills
  force_shielder: [
    {
      id: 'shield_harden',
      name: 'Shield Harden',
      description: '',
      icon: '/images/classes/passives/force_shielder/shield_harden.png',
      stats: { defense: 210, ignorePenetration: 30, damageReduce: 30 }, // Max level stats
      maxLevel: 20,
      levelStats: {
        // Shield Harden: Defense progression from 58 to 210
        defense: [
          58, 66, 74, 82, 90, 98, 106, 114, 122, 130,  // levels 1-10
          138, 146, 154, 162, 170, 178, 186, 194, 202, 210  // levels 11-20
        ],
        // Shield Harden: Ignore Penetration progression from 11 to 30
        ignorePenetration: [
          11, 12, 13, 14, 15, 16, 17, 18, 19, 20,  // levels 1-10
          21, 22, 23, 24, 25, 26, 27, 28, 29, 30   // levels 11-20
        ],
        // Shield Harden: Damage Reduction progression from 11 to 30
        damageReduce: [
          11, 12, 13, 14, 15, 16, 17, 18, 19, 20,  // levels 1-10
          21, 22, 23, 24, 25, 26, 27, 28, 29, 30   // levels 11-20
        ]
      }
    },
    {
      id: 'crushing_blade',
      name: 'Crushing Blade',
      description: '',
      icon: '/images/classes/passives/force_shielder/crushing_blade.png',
      stats: { critDamage: 60, critRate: 24 }, // Max level stats
      maxLevel: 20,
      levelStats: {
        // Crushing Blade: Critical Damage progression from 12 to 60
        critDamage: [
          12, 15, 17, 20, 22, 25, 27, 30, 32, 35,  // levels 1-10
          37, 40, 42, 45, 47, 50, 52, 55, 57, 60   // levels 11-20
        ],
        // Crushing Blade: Critical Rate progression from 5 to 24
        critRate: [
          5, 6, 7, 8, 9, 10, 11, 12, 13, 14,  // levels 1-10
          15, 16, 17, 18, 19, 20, 21, 22, 23, 24   // levels 11-20
        ]
      }
    }
  ]
};

// Helper function to get passive skills for a class
export function getPassiveSkillsForClass(classId: CharacterClass): ClassPassiveSkill[] {
  return CLASS_PASSIVE_SKILLS[classId] || [];
}

// Helper function to calculate stats for a passive skill at a specific level
export function calculatePassiveSkillStats(skill: ClassPassiveSkill, level: number): Record<string, number> {
  if (level <= 0 || level > skill.maxLevel) {
    return {};
  }

  const stats: Record<string, number> = {};
  
  if (skill.levelStats) {
    Object.entries(skill.levelStats).forEach(([statId, values]) => {
      if (values[level - 1] !== undefined) {
        stats[statId] = values[level - 1];
      }
    });
  }

  return stats;
}