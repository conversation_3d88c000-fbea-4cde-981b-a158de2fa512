/**
 * Chloe Calculator - Enhanced Table-based UI
 * Migrated from WordPress implementation with modern React patterns
 */

'use client';

import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { CHLOE_RECIPES, getRecipeCategories, type ChloeRecipe } from '../data/recipes';
import { usePriceStore } from '@/stores/priceStore';
import FilterControls from './FilterControls';
import BatchCalculateModal from './BatchCalculateModal';
import ImportPricesModal from './ImportPricesModal';
import { ProfitCalculations, type RecipeMetrics } from './ProfitCalculations';

interface SortConfig {
  key: keyof ChloeRecipe | 'profitMargin' | 'expectedProfit' | 'craftCost' | 'craftsToPayOff';
  direction: 'asc' | 'desc';
}

export default function ChloeCalculator() {
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [demandFilter, setDemandFilter] = useState<string>('');
  const [showRegisterCost, setShowRegisterCost] = useState(false);
  const [showOnlyFavorites, setShowOnlyFavorites] = useState(false);
  const [excludeMissingPrices, setExcludeMissingPrices] = useState(false);
  const [salesFee, setSalesFee] = useState(5);
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [batchModal, setBatchModal] = useState<{ isOpen: boolean; recipe: ChloeRecipe | null }>({
    isOpen: false,
    recipe: null
  });
  const [batchQuantity, setBatchQuantity] = useState(10);
  const [excludeRegisterCost, setExcludeRegisterCost] = useState(true);
  const [importModal, setImportModal] = useState(false);
  
  const { getPrice, setPrice, clearAllPrices } = usePriceStore();

  useEffect(() => {
    setMounted(true);
  }, []);
  
  const categories = getRecipeCategories();

  // Calculate recipe metrics using utility class
  const calculateRecipeMetrics = useCallback((recipe: ChloeRecipe): RecipeMetrics => {
    return ProfitCalculations.calculateRecipeMetrics(recipe, getPrice, salesFee);
  }, [getPrice, salesFee]);

  // Filter and sort recipes
  const filteredAndSortedRecipes = useMemo(() => {
    let filtered = CHLOE_RECIPES.filter(recipe => {
      const recipeId = `${recipe.name}-${recipe.recipe}`;
      
      const matchesSearch = recipe.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           recipe.recipe.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategories.length === 0 || selectedCategories.includes(recipe.category);
      const matchesDemand = !demandFilter || recipe.demand === demandFilter;
      const matchesFavorites = !showOnlyFavorites || favorites.has(recipeId);
      
      // Check for missing prices
      let matchesPriceFilter = true;
      if (excludeMissingPrices) {
        const metrics = ProfitCalculations.calculateRecipeMetrics(recipe, getPrice, salesFee);
        matchesPriceFilter = !metrics.missingAnyPrice;
      }
      
      return matchesSearch && matchesCategory && matchesDemand && matchesFavorites && matchesPriceFilter;
    });

    if (sortConfig) {
      filtered.sort((a, b) => {
        let aValue: number | string;
        let bValue: number | string;

        if (sortConfig.key === 'profitMargin' || sortConfig.key === 'expectedProfit' || 
            sortConfig.key === 'craftCost' || sortConfig.key === 'craftsToPayOff') {
          const aMetrics = calculateRecipeMetrics(a);
          const bMetrics = calculateRecipeMetrics(b);
          aValue = aMetrics[sortConfig.key];
          bValue = bMetrics[sortConfig.key];
        } else {
          aValue = a[sortConfig.key] as number | string;
          bValue = b[sortConfig.key] as number | string;
        }

        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortConfig.direction === 'asc' 
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }

        const numA = Number(aValue) || 0;
        const numB = Number(bValue) || 0;
        
        return sortConfig.direction === 'asc' ? numA - numB : numB - numA;
      });
    }

    return filtered;
  }, [CHLOE_RECIPES, searchTerm, selectedCategories, demandFilter, showOnlyFavorites, excludeMissingPrices, favorites, sortConfig, calculateRecipeMetrics, getPrice, salesFee]);

  const handleSort = (key: SortConfig['key']) => {
    setSortConfig(current => ({
      key,
      direction: current?.key === key && current.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const toggleRowExpansion = (recipeId: string) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev);
      if (newSet.has(recipeId)) {
        newSet.delete(recipeId);
      } else {
        newSet.add(recipeId);
      }
      return newSet;
    });
  };

  const toggleFavorite = (recipeId: string) => {
    setFavorites(prev => {
      const newSet = new Set(prev);
      if (newSet.has(recipeId)) {
        newSet.delete(recipeId);
      } else {
        newSet.add(recipeId);
      }
      return newSet;
    });
  };

  const openBatchModal = (recipe: ChloeRecipe) => {
    setBatchModal({ isOpen: true, recipe });
  };

  const closeBatchModal = () => {
    setBatchModal({ isOpen: false, recipe: null });
  };

  const openImportModal = () => {
    setImportModal(true);
  };

  const closeImportModal = () => {
    setImportModal(false);
  };

  const getSortIcon = (key: SortConfig['key']) => {
    if (sortConfig?.key !== key) return '↕️';
    return sortConfig.direction === 'asc' ? '↑' : '↓';
  };

  if (!mounted) {
    return (
      <div className="min-h-screen bg-theme-darkest text-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-2xl mb-2">Loading...</div>
          <div className="text-gray-400">Initializing Chloe Calculator</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-theme-darkest text-white">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="bg-component-card border border-border-dark rounded-lg p-6">
          <h1 className="text-3xl font-bold mb-6">Chloe Craft Calculator</h1>
          
          {/* Description Section */}
          <div className="space-y-4 text-gray-300">
            <div>
              <h2 className="text-xl font-semibold text-white mb-2">What is this tool?</h2>
              <p>
                The Chloe Craft Calculator helps you analyze the profitability of crafting recipes available through 
                Chloe NPCs in Cabal Online. It calculates expected profits, craft costs, and helps you make informed 
                decisions about which recipes are worth investing in.
              </p>
            </div>

            <div>
              <h2 className="text-xl font-semibold text-white mb-2">How to use</h2>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li><strong>Set item prices:</strong> Enter current market prices in the "Price Per Piece" column</li>
                <li><strong>Click on rows:</strong> Expand recipes to see detailed ingredient breakdowns</li>
                <li><strong>Use filters:</strong> Search by name, filter by category, or show only profitable recipes</li>
                <li><strong>Sort columns:</strong> Click column headers to sort by profit, cost, or success rate</li>
                <li><strong>Favorite recipes:</strong> Click the ♥ icon to mark recipes for easy access</li>
                <li><strong>Batch calculate:</strong> Use the calculator icon to plan bulk crafting sessions</li>
              </ul>
            </div>

            <div>
              <h2 className="text-xl font-semibold text-white mb-2">Understanding the calculations</h2>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-blue-400 mb-1">Expected Profit</h3>
                  <p className="text-sm">
                    This is your <em>average</em> profit per craft attempt, accounting for success rate. 
                    For example, a recipe with 80% success rate and 1000 Alz profit per success 
                    gives 800 Alz expected profit.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-blue-400 mb-1">Profit Margin</h3>
                  <p className="text-sm">
                    Percentage of revenue that becomes profit. Higher margins mean better returns 
                    on your investment, but consider the absolute profit amounts too.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-blue-400 mb-1">Register Cost</h3>
                  <p className="text-sm">
                    One-time fee to unlock each recipe. "Crafts to Pay Off" shows how many 
                    successful crafts you need to recover this investment.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-blue-400 mb-1">Sales Fee</h3>
                  <p className="text-sm">
                    Market transaction fee (default 5%). This is deducted from your revenue 
                    when selling items, affecting your actual profit.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
              <h2 className="text-xl font-semibold text-yellow-400 mb-2">⚠️ Important Notes</h2>
              <ul className="list-disc list-inside space-y-1 ml-4 text-yellow-200">
                <li><strong>RNG is real:</strong> Success rates are averages. You might get unlucky streaks or lucky streaks</li>
                <li><strong>Market prices change:</strong> Update prices regularly as market conditions fluctuate</li>
                <li><strong>Expected ≠ Guaranteed:</strong> Expected profit is statistical average, not guaranteed income</li>
                <li><strong>Consider demand:</strong> High-profit items might be hard to sell if there's no demand</li>
                <li><strong>Factor in time:</strong> Some recipes take longer to craft - consider Alz per hour, not just per craft</li>
              </ul>
            </div>

            <div className="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
              <h2 className="text-xl font-semibold text-green-400 mb-2">💡 Pro Tips</h2>
              <ul className="list-disc list-inside space-y-1 ml-4 text-green-200">
                <li>Start with recipes that have high success rates and reasonable profits</li>
                <li>Use the "Exclude Missing Prices" filter to focus on recipes you can accurately calculate</li>
                <li>Check the "Register Cost" option to see long-term profitability including unlock costs</li>
                <li>Import/export price data to share with guild members or backup your research</li>
                <li>Consider crafting multiple items to diversify risk and market exposure</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Controls */}
        <FilterControls
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          selectedCategories={selectedCategories}
          setSelectedCategories={setSelectedCategories}
          demandFilter={demandFilter}
          setDemandFilter={setDemandFilter}
          salesFee={salesFee}
          setSalesFee={setSalesFee}
          showRegisterCost={showRegisterCost}
          setShowRegisterCost={setShowRegisterCost}
          showOnlyFavorites={showOnlyFavorites}
          setShowOnlyFavorites={setShowOnlyFavorites}
          excludeMissingPrices={excludeMissingPrices}
          setExcludeMissingPrices={setExcludeMissingPrices}
          categories={categories}
          clearAllPrices={clearAllPrices}
          onImportPrices={openImportModal}
        />

        {/* Results Table */}
        <div className="bg-component-card border border-border-dark rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-theme-dark border-b border-border-dark">
                <tr>
                  <th className="px-4 py-3 text-left">⭐</th>
                  <th 
                    className="px-4 py-3 text-left cursor-pointer hover:bg-theme-light"
                    onClick={() => handleSort('name')}
                  >
                    Name {getSortIcon('name')}
                  </th>
                  <th className="px-4 py-3 text-left">Price Per Piece</th>
                  <th 
                    className="px-4 py-3 text-left cursor-pointer hover:bg-theme-light"
                    onClick={() => handleSort('craftCost')}
                  >
                    Craft Cost {getSortIcon('craftCost')}
                  </th>
                  <th 
                    className="px-4 py-3 text-left cursor-pointer hover:bg-theme-light"
                    onClick={() => handleSort('profitMargin')}
                  >
                    Profit Margin {getSortIcon('profitMargin')}
                  </th>
                  <th 
                    className="px-4 py-3 text-left cursor-pointer hover:bg-theme-light"
                    onClick={() => handleSort('expectedProfit')}
                  >
                    Expected Profit {getSortIcon('expectedProfit')}
                  </th>
                  {showRegisterCost && (
                    <>
                      <th className="px-4 py-3 text-left">Register Cost</th>
                      <th 
                        className="px-4 py-3 text-left cursor-pointer hover:bg-theme-light"
                        onClick={() => handleSort('craftsToPayOff')}
                      >
                        Crafts to Pay Off {getSortIcon('craftsToPayOff')}
                      </th>
                    </>
                  )}
                  <th className="px-4 py-3 text-left">Success Rate</th>
                  <th className="px-4 py-3 text-left">Req. Amity</th>
                </tr>
              </thead>
              <tbody>
                {filteredAndSortedRecipes.map((recipe) => {
                  const recipeId = `${recipe.name}-${recipe.recipe}`;
                  const isExpanded = expandedRows.has(recipeId);
                  const isFavorite = favorites.has(recipeId);
                  const metrics = calculateRecipeMetrics(recipe);
                  const outputPrice = getPrice(recipe.name);

                  return (
                    <React.Fragment key={recipeId}>
                      <tr 
                        className="border-b border-border-dark hover:bg-theme-light cursor-pointer transition-colors"
                        onClick={() => toggleRowExpansion(recipeId)}
                      >
                        <td className="px-4 py-3">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleFavorite(recipeId);
                            }}
                            className={`text-lg ${isFavorite ? 'text-red-500' : 'text-gray-400'} hover:text-red-400`}
                          >
                            ♥
                          </button>
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex items-center space-x-3">
                            <img 
                              src={ProfitCalculations.getIconPath(recipe.iconPath)} 
                              alt={recipe.name}
                              className="w-8 h-8 object-contain"
                              onError={(e) => {
                                // Fallback to a default icon or hide if image fails to load
                                e.currentTarget.style.display = 'none';
                              }}
                            />
                            <div className="flex items-center space-x-2">
                              <span className="font-medium">{recipe.recipe}</span>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <input
                            type="number"
                            value={outputPrice || ''}
                            onChange={(e) => {
                              e.stopPropagation();
                              setPrice(recipe.name, Number(e.target.value) || 0);
                            }}
                            className="w-24 bg-theme-dark border border-border-dark rounded px-2 py-1 text-white text-sm focus:border-blue-500 focus:outline-none [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-inner-spin-button]:m-0 [&::-webkit-outer-spin-button]:m-0 [-moz-appearance:textfield]"
                            placeholder="0"
                          />
                        </td>
                        <td className="px-4 py-3 text-red-400">
                          {ProfitCalculations.formatNumber(metrics.craftCost)}
                        </td>
                        <td className={`px-4 py-3 ${metrics.profitMargin >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          {metrics.profitMargin.toFixed(2)}%
                        </td>
                        <td className={`px-4 py-3 ${metrics.expectedProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          {ProfitCalculations.formatNumber(metrics.expectedProfit)}
                          {metrics.missingAnyPrice && (
                            <span 
                              className="ml-2 text-yellow-400" 
                              title="Missing ingredient or recipe price - calculations may be inaccurate"
                            >
                              ⚠️
                            </span>
                          )}
                        </td>
                        {showRegisterCost && (
                          <>
                            <td className="px-4 py-3 text-yellow-400">
                              {ProfitCalculations.formatNumber(recipe.registerCost)}
                            </td>
                            <td className="px-4 py-3 text-blue-400">
                              {metrics.craftsToPayOff > 0 ? ProfitCalculations.formatNumber(metrics.craftsToPayOff) : '-'}
                            </td>
                          </>
                        )}
                        <td className="px-4 py-3">{recipe.successRate}%</td>
                        <td className="px-4 py-3">{recipe.requiredAmity || '0'}</td>
                      </tr>
                      
                      {/* Expanded Row Details */}
                      {isExpanded && (
                        <tr className="bg-theme-dark/50">
                          <td colSpan={showRegisterCost ? 10 : 8} className="px-6 py-6">
                            <div className="max-w-4xl mx-auto space-y-4">
                              {/* Header with title and batch button */}
                              <div className="flex justify-between items-center mb-4">
                                <div className="text-center flex-1">
                                  <p className="text-gray-300 text-sm">
                                    Ingredients required to craft <span className="text-blue-400 font-medium">{recipe.recipe}</span>
                                  </p>
                                </div>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    openBatchModal(recipe);
                                  }}
                                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors shadow-lg"
                                >
                                  Batch Calculate
                                </button>
                              </div>
                              
                              {/* Ingredients Table */}
                              <div className="bg-theme-darkest/60 rounded-xl overflow-hidden border border-border-dark shadow-lg">
                                <table className="w-full">
                                  <thead>
                                    <tr className="bg-theme-dark border-b border-border-dark">
                                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-300">Item</th>
                                      <th className="px-4 py-3 text-center text-sm font-medium text-gray-300">Quantity</th>
                                      <th className="px-4 py-3 text-center text-sm font-medium text-gray-300">Price per unit</th>
                                      <th className="px-6 py-3 text-right text-sm font-medium text-gray-300">Total</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {recipe.ingredients.map((ingredient, index) => {
                                      const price = getPrice(ingredient.name);
                                      const total = (price || 0) * ingredient.quantity;
                                      const isLastRow = index === recipe.ingredients.length - 1;
                                      
                                      return (
                                        <tr 
                                          key={`${recipeId}-ingredient-${index}`} 
                                          className={`hover:bg-theme-light/30 transition-colors ${!isLastRow ? 'border-b border-border-dark/50' : ''}`}
                                        >
                                          <td className="px-6 py-4 text-gray-200 font-medium">{ingredient.name}</td>
                                          <td className="px-4 py-4 text-center text-gray-300">{ingredient.quantity}</td>
                                          <td className="px-4 py-4 text-center">
                                            <input
                                              type="number"
                                              value={price || ''}
                                              onChange={(e) => {
                                                e.stopPropagation();
                                                setPrice(ingredient.name, Number(e.target.value) || 0);
                                              }}
                                              className="w-24 bg-theme-darkest border border-border-dark rounded-lg px-3 py-2 text-gray-200 text-center text-sm focus:border-blue-500 focus:outline-none transition-colors placeholder-gray-500 [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-inner-spin-button]:m-0 [&::-webkit-outer-spin-button]:m-0 [-moz-appearance:textfield]"
                                              placeholder="0"
                                            />
                                          </td>
                                          <td className="px-6 py-4 text-right text-yellow-400 font-medium">{ProfitCalculations.formatNumber(total)}</td>
                                        </tr>
                                      );
                                    })}
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  );
                })}
              </tbody>
            </table>
          </div>
          
          {filteredAndSortedRecipes.length === 0 && (
            <div className="text-center py-8 text-gray-400">
              No recipes found matching your criteria.
            </div>
          )}
        </div>

        {/* Batch Calculation Modal */}
        <BatchCalculateModal
          isOpen={batchModal.isOpen}
          recipe={batchModal.recipe}
          batchQuantity={batchQuantity}
          setBatchQuantity={setBatchQuantity}
          excludeRegisterCost={excludeRegisterCost}
          setExcludeRegisterCost={setExcludeRegisterCost}
          getPrice={getPrice}
          salesFee={salesFee}
          onClose={closeBatchModal}
        />

        {/* Import Prices Modal */}
        <ImportPricesModal
          isOpen={importModal}
          onClose={closeImportModal}
        />
      </div>
    </div>
  );
}