/**
 * Stats Configuration
 * Central configuration for stats shared across all systems
 */

import { getSpriteData } from '../utils/spriteIconUtils';

export interface StatInfo {
  name: string;
  category: 'offensive' | 'defensive' | 'utility';
  isPercentage: boolean;
  description: string;
  variants?: string[];
  icon?: string;
  spriteData?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface StatsConfig {
  baseStats: {
    critRate: number;
    maxCritRate: number;
    critDamage: number;
  };
  stats: Record<string, StatInfo>;
}

export const statsConfig: StatsConfig = {
  // Base stats that all characters have by default
  // These are universal constants that always apply to every character
  baseStats: {
    critRate: 5,        // Base critical rate is 5%
    maxCritRate: 50,    // Base max critical rate is 50%
    critDamage: 20      // Base critical damage is 20%
  },

  // Comprehensive stat definitions
  stats: {
    //======================================
    // OFFENSIVE STATS
    //======================================

    // Base attack stats
    attack: {
      name: "Attack",
      category: "offensive",
      isPercentage: false,
      description: "Increases physical damage",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/attack_icon.png"
    },

    magicAttack: {
      name: "Magic Attack",
      category: "offensive",
      isPercentage: false,
      description: "Increases magic damage",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/mattack_icon.png"
    },

    attackRate: {
      name: "Attack Rate",
      category: "offensive",
      isPercentage: false,
      description: "Increases attack speed",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/attack_rate_icon.png"
    },

    critRate: {
      name: "Critical Rate",
      category: "offensive",
      isPercentage: true,
      description: "Increases chance of landing critical hits",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/critical_rate_icon.png"
    },

    maxCritRate: {
      name: "Max Crit. Rate",
      category: "offensive",
      isPercentage: true,
      description: "Maximum critical rate cap",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/max_crit_rate_icon.png"
    },

    critDamage: {
      name: "Critical DMG",
      category: "offensive",
      isPercentage: true,
      description: "Increases critical hit damage",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/critical_damage_icon.png"
    },

    swordSkillAmp: {
      name: "Sword Skill Amp.",
      category: "offensive",
      isPercentage: true,
      description: "Increases sword skill damage",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/sword_amp_icon.png"
    },

    magicSkillAmp: {
      name: "Magic Skill Amp.",
      category: "offensive",
      isPercentage: true,
      description: "Increases magic skill damage",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/magic_amp_icon.png"
    },

    accuracy: {
      name: "Accuracy",
      category: "offensive",
      isPercentage: false,
      description: "Reduces chance of attacks being evaded",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/accuracy_icon.png"
    },

    penetration: {
      name: "Penetration",
      category: "offensive",
      isPercentage: false,
      description: "Bypasses a portion of enemy defense",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/penetration_icon.png"
    },

    addDamage: {
      name: "Add. Damage",
      category: "offensive",
      isPercentage: false,
      description: "Flat damage added to attacks",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/add_dmg_icon.png"
    },

    minDamage: {
      name: "Min Damage",
      category: "offensive",
      isPercentage: true,
      description: "Increases minimum damage",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/min_dmg_icon.png"
    },

    ignoreEvasion: {
      name: "Ignore Evasion",
      category: "offensive",
      isPercentage: false,
      description: "Reduces chance of being evaded",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/ignore_evasion_icon.png"
    },

    cancelIgnoreEvasion: {
      name: "Cancel Ignore Evasion",
      category: "offensive",
      isPercentage: false,
      description: "Reduces chance of being evaded",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/cancel_ignore_evasion_icon.png"
    },
    

    finalDamageIncreased: {
      name: "Final DMG Increased",
      category: "offensive",
      isPercentage: true,
      description: "Increases final damage",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/final_dmg_increase_icon.png"
    },

    finalDamageDecreased: {
      name: "Final DMG Decreased",
      category: "offensive",
      isPercentage: true,
      description: "Decreases final damage",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/final_dmg_decrease_icon.png"
    },


    ignoreDamageReduce: {
      name: "Ignore DMG Reduction",
      category: "offensive",
      isPercentage: false,
      description: "Bypasses enemy damage reduction",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/ignore_dmg_reduce_icon.png"
    },

    ignoreResistCritRate: {
      name: "Ignore Resist Critical Rate",
      category: "offensive",
      isPercentage: true,
      description: "Bypasses enemy critical rate resistance",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/ignore_resist_crit_rate_icon.png"
    },

    ignoreResistCritDmg: {
      name: "Ignore Resist Critical DMG",
      category: "offensive",
      isPercentage: true,
      description: "Bypasses enemy critical damage resistance",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/ignore_resist_crit_dmg_icon.png"
    },

    cancelIgnoreDamageReduce: {
      name: "Cancel Ignore Damage Reduction",
      category: "offensive",
      isPercentage: false,
      description: "Reduces enemy damage reduction",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/cancel_ignore_damage_reduction_icon.png"
    },

    ignoreResistSkillAmp: {
      name: "Ignore Resist Skill Amp",
      category: "offensive",
      isPercentage: true,
      description: "Bypasses enemy skill amplification resistance",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/ignore_resist_skill_amp_icon.png"
    },

    normalDamageUp: {
      name: "Normal DMG Up",
      category: "offensive",
      isPercentage: true,
      description: "Increases normal attack damage",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/normal_dmg_icon.png"
    },

    cancelIgnorePenetration: {
      name: "Cancel Ignore Penetration",
      category: "offensive",
      isPercentage: false,
      description: "Reduces enemy penetration",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/cancel_ignore_penetration_icon.png"
    },

    allAttackUp: {
      name: "All Attack Up",
      category: "offensive",
      isPercentage: false,
      description: "Increases all attack types",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/all_atk_icon.png"
    },

    allSkillAmp: {
      name: "All Skill Amp.",
      category: "offensive",
      isPercentage: true,
      description: "Increases all skill damage",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/all_amp_icon.png"
    },

    //======================================
    // DEFENSIVE STATS
    //======================================

    hp: {
      name: "HP",
      category: "defensive",
      isPercentage: false,
      description: "Total health points",
      icon: "/images/stat icons/hp_icon.png"
    },

    defense: {
      name: "Defense",
      category: "defensive",
      isPercentage: false,
      description: "Reduces damage taken",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/defense_icon.png"
    },

    defenseRate: {
      name: "Defense Rate",
      category: "defensive",
      isPercentage: false,
      description: "Increases damage reduction",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/defense_rate_icon.png"
    },

    evasion: {
      name: "Evasion",
      category: "defensive",
      isPercentage: false,
      description: "Chance to evade attacks",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/evasion_icon.png"
    },

    resistCritRate: {
      name: "Resist Critical Rate",
      category: "defensive",
      isPercentage: true,
      description: "Reduces chance of receiving critical hits",
      icon: "/images/stat icons/resist_critical_rate_icon.png"
    },

    resistCritDmg: {
      name: "Resist Critical DMG",
      category: "defensive",
      isPercentage: true,
      description: "Reduces critical damage taken",
      icon: "/images/stat icons/resist_crit_dmg_icon.png"
    },

    resistSkillAmp: {
      name: "Resist Skill Amp",
      category: "defensive",
      isPercentage: true,
      description: "Reduces skill damage taken (increases both sword and magic skill amp resistance)",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/resist_skill_amp_icon.png"
    },

    resistMagicSkillAmp: {
      name: "Resist Magic Skill Amp",
      category: "defensive",
      isPercentage: true,
      description: "Reduces magic skill damage taken",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/resist_magic_skill_amp_icon.png"
    },

    resistSwordSkillAmp: {
      name: "Resist Sword Skill Amp",
      category: "defensive",
      isPercentage: true,
      description: "Reduces sword skill damage taken",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/resist_sword_skill_amp_icon.png"
    },

    ignorePenetration: {
      name: "Ignore Penetration",
      category: "defensive",
      isPercentage: false,
      description: "Reduces enemy penetration",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/ignore_penetration_icon.png"
    },

    ignoreAccuracy: {
      name: "Ignore Accuracy",
      category: "defensive",
      isPercentage: false,
      description: "Increases chance to evade despite enemy accuracy",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/ignore_accuracy_icon.png"
    },

    damageReduce: {
      name: "DMG Reduction",
      category: "defensive",
      isPercentage: false,
      description: "Reduces all damage taken",
      variants: ["pvp", "pve"],
      icon: "/images/stat icons/dmg_reduction_icon.png"
    },

    resistSuppression: {
      name: "Resist Suppression",
      category: "defensive",
      isPercentage: true,
      description: "Reduces chance of being suppressed",
      icon: "/images/stat icons/resist_suppression_icon.png"
    },

    resistSilence: {
      name: "Resist Silence",
      category: "defensive",
      isPercentage: true,
      description: "Reduces chance of being silenced",
      icon: "/images/stat icons/resist_silence_icon.png"
    },

    //======================================
    // UTILITY STATS
    //======================================

    mp: {
      name: "MP",
      category: "utility",
      isPercentage: false,
      description: "Total mana points",
      icon: "/images/stat icons/mana_icon.png"
    },

    hpAbsorb: {
      name: "HP Absorb",
      category: "utility",
      isPercentage: true,
      description: "Amount of HP absorbed per hit",
      icon: "/images/stat icons/hp_absorb_icon.png"
    },

    maxHpSteal: {
      name: "Max HP Steal Per Hit",
      category: "utility",
      isPercentage: false,
      description: "Increases HP absorption",
      icon: "/images/stat icons/max_hp_absorb_icon.png"
    },

    mpAbsorb: {
      name: "MP Absorb",
      category: "utility",
      isPercentage: true,
      description: "Amount of MP absorbed per hit",
      icon: "/images/stat icons/mp_absorb_icon.png"
    },

    maxMpSteal: {
      name: "Max MP Steal Per Hit",
      category: "utility",
      isPercentage: false,
      description: "Increases MP absorption",
      icon: "/images/stat icons/max_mp_absorb_icon.png"
    },

    hpAutoHeal: {
      name: "HP Auto Heal",
      category: "utility",
      isPercentage: false,
      description: "HP regenerated over time",
      icon: "/images/stat icons/hp_auto_heal_icon.png"
    },

    mpAutoHeal: {
      name: "MP Auto Heal",
      category: "utility",
      isPercentage: false,
      description: "MP regenerated over time",
      icon: "/images/stat icons/mp_auto_heal_icon.png"
    },

    exp: {
      name: "EXP",
      category: "utility",
      isPercentage: true,
      description: "Increases experience gain",
      icon: "/images/stat icons/exp_icon.png"
    },

    skillExp: {
      name: "Skill EXP",
      category: "utility",
      isPercentage: true,
      description: "Increases skill experience gain",
      icon: "/images/stat icons/skill_exp_icon.png"
    },

    partyExp: {
      name: "Party EXP",
      category: "utility",
      isPercentage: true,
      description: "Increases party experience gain",
      icon: "/images/stat icons/party_exp_icon.png"
    },

    petExp: {
      name: "Pet EXP",
      category: "utility",
      isPercentage: true,
      description: "Increases pet experience gain"
    },

    alzDropAmount: {
      name: "Alz Drop Amount",
      category: "utility",
      isPercentage: true,
      description: "Increases Alz drops",
      icon: "/images/stat icons/alz_drop_amount_icon.png"
    },

    alzDropRate: {
      name: "Alz Drop Rate",
      category: "utility",
      isPercentage: true,
      description: "Increases chance of Alz drops",
      icon: "/images/stat icons/alz_drop_rate_icon.png"
    },

    alzBombChance: {
      name: "Alz Bomb Chance",
      category: "utility",
      isPercentage: true,
      description: "Increases chance of getting big Alz drops",
      icon: "/images/stat icons/alz_bomb_chance_icon.png"
    },

    '2SlotDropRate': {
      name: "2-Slot Drop Rate",
      category: "utility",
      isPercentage: true,
      description: "Increases 2-slot item drop rate",
      icon: "/images/stat icons/2_slot_item_drop_icon.png"
    },

    resistUnableToMove: {
      name: "Resist Unable to Move",
      category: "utility",
      isPercentage: true,
      description: "Reduces chance of movement-impairing effects",
      icon: "/images/stat icons/resist_unable_move_icon.png"
    },

    resistDown: {
      name: "Resist Down",
      category: "utility",
      isPercentage: true,
      description: "Reduces chance of knockdown",
      icon: "/images/stat icons/resist_down_icon.png"
    },

    resistKnockback: {
      name: "Resist Knockback",
      category: "utility",
      isPercentage: true,
      description: "Reduces chance of being knocked back",
      icon: "/images/stat icons/resist_knock_back_icon.png"
    },

    resistStun: {
      name: "Resist Stun",
      category: "utility",
      isPercentage: true,
      description: "Reduces chance of being stunned",
      icon: "/images/stat icons/stun_resist_icon.png"
    },

    ignoreResistKnockback: {
      name: "Ignore Resist Knockback",
      category: "utility",
      isPercentage: true,
      description: "Bypasses enemy knockback resistance",
      icon: "/images/stat icons/ignore_resist_knockback_icon.png"
    },

    ignoreResistDown: {
      name: "Ignore Resist Down",
      category: "utility",
      isPercentage: true,
      description: "Bypasses enemy knockdown resistance",
      icon: "/images/stat icons/ignore_resist_down_icon.png"
    },

    ignoreResistStun: {
      name: "Ignore Resist Stun",
      category: "utility",
      isPercentage: true,
      description: "Bypasses enemy stun resistance",
      icon: "/images/stat icons/ignore_resist_stun_icon.png"
    },

    auraDurationIncrease: {
      name: "Aura Duration Increase",
      category: "utility",
      isPercentage: false,
      description: "Extends aura skill duration",
      icon: "/images/stat icons/aura_duration_increase_icon.png"
    },

    battleModeDurationIncrease: {
      name: "Battle Mode Duration Increase",
      category: "utility",
      isPercentage: false,
      description: "Extends battle mode duration",
      icon: "/images/stat icons/battle_mode_duration_increase_icon.png"
    },

    honourPoint: {
      name: "Honour Point",
      category: "utility",
      isPercentage: false,
      description: "Points used for honour-related activities"
    },

    upgradeRate: {
      name: "Upgrade Rate",
      category: "utility",
      isPercentage: true,
      description: "Increases chance of successful upgrades"
    },

    movementSpeed: {
      name: "Movement Speed",
      category: "utility",
      isPercentage: false,
      description: "Increases character movement speed"
    },

    boostHpRestoration: {
      name: "Boost HP Restoration",
      category: "utility",
      isPercentage: false,
      description: "Increases the amount of HP restored by skills/potions"
    },

    // SPECIAL STATS; DERIVED STATS; THESE STATS SIMPLY INCREASE OTHER STATS
    str: {
      name: "STR",
      category: "offensive",
      isPercentage: false,
      description: "Strength stat",
      icon: "/images/stat icons/str_icon.png"
    },

    int: {
      name: "INT",
      category: "offensive",
      isPercentage: false,
      description: "Intelligence stat",
      icon: "/images/stat icons/int_icon.png"
    },

    dex: {
      name: "DEX",
      category: "offensive",
      isPercentage: false,
      description: "Dexterity stat",
      icon: "/images/stat icons/dex_icon.png"
    }
  }
};

// Helper functions
export function getStatInfo(statId: string): StatInfo | null {
  // Direct lookup first - for exact matches
  if (statsConfig.stats[statId]) {
    const stat = statsConfig.stats[statId];
    const spriteData = stat.icon ? getSpriteData(stat.icon) : null;
    return {
      ...stat,
      spriteData: spriteData || undefined
    };
  }
  
  // Check if this is a variant (like "pvpAttack")
  if (statId.startsWith('pvp') || statId.startsWith('pve')) {
    const prefix = statId.substring(0, 3);
    const baseStatId = statId.substring(3, 4).toLowerCase() + statId.substring(4);

    if (statsConfig.stats[baseStatId] &&
        statsConfig.stats[baseStatId].variants &&
        statsConfig.stats[baseStatId].variants.includes(prefix)) {

      const baseStat = statsConfig.stats[baseStatId];
      
      // Try to find variant-specific icon, fallback to base icon
      let variantIcon = baseStat.icon;
      if (baseStat.icon) {
        const iconPath = baseStat.icon.replace('/images/stat icons/', '').replace('.png', '');
        const variantIconPath = `/images/stat icons/${prefix}_${iconPath}.png`;
        variantIcon = variantIconPath;
      }

      // Return a new object with variant-specific properties
      const variantSpriteData = variantIcon ? getSpriteData(variantIcon) : null;
      return {
        name: `${prefix.toUpperCase()} ${baseStat.name}`,
        category: baseStat.category,
        isPercentage: baseStat.isPercentage,
        description: `${baseStat.description} against ${prefix === 'pvp' ? 'players' : 'monsters'}`,
        icon: variantIcon,
        spriteData: variantSpriteData || undefined
      };
    }
  }

  return null;
}

export function formatStatValue(statId: string, value: number): string {
  const stat = getStatInfo(statId);
  if (!stat) return value.toString();

  return stat.isPercentage ? value + '%' : value.toString();
}

export function getStatsByCategory(category: 'offensive' | 'defensive' | 'utility'): string[] {
  const result: string[] = [];

  // First add all base stats in this category
  for (const statId in statsConfig.stats) {
    if (statsConfig.stats[statId].category === category) {
      result.push(statId);

      // Then add any variants
      if (statsConfig.stats[statId].variants) {
        statsConfig.stats[statId].variants.forEach(variant => {
          result.push(variant + statId.charAt(0).toUpperCase() + statId.slice(1));
        });
      }
    }
  }

  return result;
}