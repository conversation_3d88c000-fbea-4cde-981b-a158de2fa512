/**
 * Enhanced Damage Calculation Modal
 * Shows detailed step-by-step damage calculations for debugging and understanding
 */

'use client';

import React from 'react';
import { useBuildPlannerStore } from '@/tools/build-planner/stores/buildPlannerStore';
import { useStatRegistryStore } from '@/tools/build-planner/stores/statRegistryStore';
import { getAllCombinedStats } from '@/tools/build-planner/utils/statCombinationUtils';
import { getClassDamageType, getClassPrimaryStats } from '@/tools/build-planner/utils/classDamageUtils';
import { calculateDamageWithSteps, type DamageCalculationStats } from '@/tools/build-planner/utils/damageCalculationUtils';
import { useClassStore } from '@/tools/build-planner/systems/class/stores';
import { IoClose } from 'react-icons/io5';
import type { CharacterClass } from '@/tools/build-planner/systems/class/types';

interface DamageCalculationModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
}

// Helper function to format numbers with commas
const formatNumber = (num: number): string => {
  return num.toLocaleString('en-US');
};

const DamageCalculationModal: React.FC<DamageCalculationModalProps> = ({
  isOpen,
  onClose,
  title
}) => {
  const buildStats = useBuildPlannerStore((state) => state.buildStats);
  const damageStats = useBuildPlannerStore((state) => state.damageStats);
  const characterLevel = useBuildPlannerStore((state) => state.characterLevel);
  const enemyConfig = useBuildPlannerStore((state) => state.enemyConfig);
  const selectedClass = useClassStore((state) => state.selectedClass);
  const getAllStats = useStatRegistryStore((state) => state.getAllStats);

  if (!isOpen || !selectedClass) return null;

  // Get combined stats for PvE (always use PvE for damage calculations)
  const combinedStats = getAllCombinedStats(buildStats, selectedClass, 'pve');
  
  // Convert to DamageCalculationStats format
  const calculationStats: DamageCalculationStats = {
    attack: combinedStats.attack || 0,
    magicAttack: combinedStats.magicAttack || 0,
    penetration: combinedStats.penetration || 0,
    cancelIgnorePenetration: combinedStats.cancelIgnorePenetration || 0,
    critDamage: combinedStats.critDamage || 0,
    normalDamageUp: combinedStats.normalDamageUp || 0,
    skillAmp: combinedStats.skillAmp || 0,
    swordSkillAmp: combinedStats.swordSkillAmp || 0,
    magicSkillAmp: combinedStats.magicSkillAmp || 0,
    addDamage: combinedStats.addDamage || 0,
    finalDamageIncreased: combinedStats.finalDamageIncreased || 0,
    ignoreDamageReduce: combinedStats.ignoreDamageReduce || 0,
    ignoreResistCritDmg: combinedStats.ignoreResistCritDmg || 0,
  };

  // Calculate damage with detailed steps using the shared utility
  const damageResult = calculateDamageWithSteps(
    calculationStats,
    characterLevel,
    enemyConfig,
    selectedClass,
    true // Include detailed steps
  );

  // Extract the calculation steps
  const steps = damageResult.steps!;
  
  // Extract values for display (using the steps data)
  const {
    baseAttackValue,
    totalSkillAmp,
    playerLevel,
    enemyLevel,
    levelDifference,
    levelPenaltyPercent,
    basePenetration,
    enemyIgnorePenetration,
    cancelIgnorePenetration,
    effectiveEnemyIgnorePenetration,
    effectivePenetration,
    enemyDefense,
    finalDefenseReduction: defenseReduction,
    baseDamage,
    amplifiedDamage,
    levelAdjustedDamage,
    defenseAdjustedDamage,
    withAddDamage,
    afterDamageReduction,
    finalBaseDamage,
    minNormalDamage,
    maxNormalDamage,
    minCriticalDamage,
    maxCriticalDamage,
    damageType,
    // Critical damage resistance values
    critDamage,
    ignoreResistCritDmg,
    enemyResistCritDmg,
    effectiveEnemyResistCritDmg,
    effectiveCritDamage,
    // Ignore damage reduction values
    ignoreDamageReduce,
    enemyDamageReduction,
    enemyDamageReductionPercent,
    effectiveEnemyDamageReduction,
    effectiveEnemyDamageReductionPercent
  } = steps;

  // Extract stats for display
  const normalDamageUp = calculationStats.normalDamageUp;
  const addDamage = calculationStats.addDamage;
  const finalDamageIncreased = calculationStats.finalDamageIncreased;

  // Extract enemy stats for display
  const enemyFinalDamageDecrease = enemyConfig.finalDamageDecrease || 0;

  const StepRow: React.FC<{ 
    step: string; 
    description: string; 
    minValue: number; 
    maxValue: number; 
    formula?: string;
    highlight?: boolean;
  }> = ({ step, description, minValue, maxValue, formula, highlight = false }) => (
    <div className={`grid grid-cols-3 gap-3 py-2 px-3 rounded-md transition-colors ${
      highlight 
        ? 'bg-game-gold/10 border border-game-gold/30 glow-border' 
        : 'hover:bg-theme-light/50'
    }`}>
      <div className="font-medium text-game-gold text-sm">{step}</div>
      <div className="text-xs text-gray-300 font-mono break-all">{formula || ''}</div>
      <div className="text-right font-mono text-white text-sm">
        {formatNumber(Math.round(minValue))}
        {minValue !== maxValue && ` - ${formatNumber(Math.round(maxValue))}`}
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="glass-panel-dark max-w-6xl w-full max-h-[90vh] overflow-hidden shadow-game">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border-light">
          <h2 className="text-xl font-bold text-game-gold glow-text-sm">{title} - Step-by-Step Calculation</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-game-gold transition-colors p-1 rounded-md hover:bg-theme-light/30"
          >
            <IoClose size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-4 overflow-y-auto max-h-[calc(90vh-80px)] dark-scrollbar">
          {/* Input Values */}
          <div className="mb-6 glass-panel-light p-4">
            <h3 className="text-sm font-semibold text-game-gold mb-3 glow-text-sm">Input Values</h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 text-xs">
              <div className="text-gray-300">Player Lvl: <span className="text-white font-mono">{playerLevel}</span></div>
              <div className="text-gray-300">Enemy Lvl: <span className="text-white font-mono">{enemyLevel}</span></div>
              <div className="text-gray-300">Base Attack: <span className="text-white font-mono">{formatNumber(baseAttackValue)}</span></div>
              <div className="text-gray-300">Skill Amp: <span className="text-stat-offensive font-mono">{totalSkillAmp}%</span></div>
              <div className="text-gray-300">Base Penetration: <span className="text-white font-mono">{formatNumber(basePenetration)}</span></div>
              <div className="text-gray-300">Enemy Ignore Pen: <span className="text-red-400 font-mono">{formatNumber(enemyIgnorePenetration)}</span></div>
              <div className="text-gray-300">Cancel Ignore Pen: <span className="text-stat-utility font-mono">{formatNumber(cancelIgnorePenetration)}</span></div>
              <div className="text-gray-300">Effective Enemy Ignore: <span className="text-orange-400 font-mono">{formatNumber(effectiveEnemyIgnorePenetration)}</span></div>
              <div className="text-gray-300">Effective Pen: <span className="text-yellow-400 font-mono">{formatNumber(effectivePenetration)}</span></div>
              <div className="text-gray-300">Enemy Defense: <span className="text-white font-mono">{formatNumber(enemyDefense)}</span></div>
              <div className="text-gray-300">Normal DMG Up: <span className="text-stat-offensive font-mono">{normalDamageUp}%</span></div>
              <div className="text-gray-300">Base Critical DMG: <span className="text-stat-offensive font-mono">{critDamage}%</span></div>
              <div className="text-gray-300">Enemy Resist Crit DMG: <span className="text-red-400 font-mono">{enemyResistCritDmg}%</span></div>
              <div className="text-gray-300">Ignore Resist Crit DMG: <span className="text-stat-utility font-mono">{ignoreResistCritDmg}%</span></div>
              <div className="text-gray-300">Effective Enemy Resist: <span className="text-orange-400 font-mono">{effectiveEnemyResistCritDmg}%</span></div>
              <div className="text-gray-300">Effective Critical DMG: <span className="text-yellow-400 font-mono">{effectiveCritDamage}%</span></div>
              <div className="text-gray-300">Enemy DMG Reduction: <span className="text-red-400 font-mono">{enemyDamageReduction}</span></div>
              <div className="text-gray-300">Enemy DMG Reduction %: <span className="text-red-400 font-mono">{enemyDamageReductionPercent}%</span></div>
              <div className="text-gray-300">Ignore DMG Reduction: <span className="text-stat-utility font-mono">{ignoreDamageReduce}</span></div>
              <div className="text-gray-300">Effective Enemy DMG Red: <span className="text-orange-400 font-mono">{effectiveEnemyDamageReduction}</span></div>
              <div className="text-gray-300">Effective Enemy DMG Red %: <span className="text-orange-400 font-mono">{effectiveEnemyDamageReductionPercent}%</span></div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
            {/* Normal Hit Calculation */}
            <div className="glass-panel border-l-4 border-stat-defensive">
              <div className="p-4 pb-3 border-b border-border-light">
                <h3 className="text-lg font-semibold text-stat-defensive glow-text-sm">Normal Hit Step-by-Step</h3>
              </div>
              <div className="p-3">
                <div className="grid grid-cols-3 gap-3 py-2 px-3 bg-theme-light/30 rounded-md font-semibold text-xs text-gray-300 mb-2">
                  <div>Step</div>
                  <div>Calculation</div>
                  <div className="text-right">Result</div>
                </div>

                <StepRow 
                  step="1. Base" 
                  description="" 
                  minValue={baseDamage} 
                  maxValue={baseDamage}
                  formula={`${formatNumber(baseAttackValue)}`}
                />

                <StepRow 
                  step="2. Skill Amp" 
                  description="" 
                  minValue={amplifiedDamage} 
                  maxValue={amplifiedDamage}
                  formula={`${formatNumber(baseAttackValue)} × ${(100 + totalSkillAmp)/100}`}
                />

                <StepRow 
                  step="3. Level Penalty" 
                  description="" 
                  minValue={levelAdjustedDamage} 
                  maxValue={levelAdjustedDamage}
                  formula={levelDifference > 0 ? `${formatNumber(Math.round(amplifiedDamage))} × ${(100 - levelPenaltyPercent)/100}` : `No penalty (${playerLevel} vs ${enemyLevel})`}
                />

                {enemyDefense > 0 && (
                  <StepRow 
                    step="4a. Base Defense" 
                    description="" 
                    minValue={enemyDefense > 0 ? ((1 - (1 / (1 + enemyDefense / 1000))) * 100) : 0} 
                    maxValue={enemyDefense > 0 ? ((1 - (1 / (1 + enemyDefense / 1000))) * 100) : 0}
                    formula={`1 - 1/(1 + ${formatNumber(enemyDefense)}/1000) = ${((1 - (1 / (1 + enemyDefense / 1000))) * 100).toFixed(2)}%`}
                  />
                )}

                {enemyDefense > 0 && (enemyIgnorePenetration > 0 || cancelIgnorePenetration > 0) && (
                  <StepRow 
                    step="4b. Enemy Ignore Red" 
                    description="" 
                    minValue={effectiveEnemyIgnorePenetration} 
                    maxValue={effectiveEnemyIgnorePenetration}
                    formula={`max(0, ${formatNumber(enemyIgnorePenetration)} - ${formatNumber(cancelIgnorePenetration)}) = ${formatNumber(effectiveEnemyIgnorePenetration)}`}
                  />
                )}

                {enemyDefense > 0 && (basePenetration > 0 || effectiveEnemyIgnorePenetration > 0) && (
                  <StepRow 
                    step="4c. Effective Pen" 
                    description="" 
                    minValue={effectivePenetration} 
                    maxValue={effectivePenetration}
                    formula={`max(0, ${formatNumber(basePenetration)} - ${formatNumber(effectiveEnemyIgnorePenetration)}) = ${formatNumber(effectivePenetration)}`}
                  />
                )}

                {enemyDefense > 0 && effectivePenetration > 0 && (
                  <StepRow 
                    step="4d. Pen Reduction" 
                    description="" 
                    minValue={((effectivePenetration / enemyDefense) * 100)} 
                    maxValue={((effectivePenetration / enemyDefense) * 100)}
                    formula={`${formatNumber(effectivePenetration)}/${formatNumber(enemyDefense)} = ${((effectivePenetration / enemyDefense) * 100).toFixed(2)}% reduction`}
                  />
                )}

                <StepRow 
                  step="4e. Final Defense" 
                  description="" 
                  minValue={defenseAdjustedDamage} 
                  maxValue={defenseAdjustedDamage}
                  formula={enemyDefense > 0 ? `Final: ${(defenseReduction * 100).toFixed(2)}% → ${formatNumber(Math.round(levelAdjustedDamage))} × ${(1 - defenseReduction).toFixed(3)}` : `No defense`}
                />

                {addDamage > 0 && (
                  <StepRow 
                    step="5. Add DMG" 
                    description="" 
                    minValue={withAddDamage} 
                    maxValue={withAddDamage}
                    formula={`${formatNumber(Math.round(defenseAdjustedDamage))} + ${formatNumber(addDamage)}`}
                  />
                )}

                {(enemyDamageReduction > 0 || enemyDamageReductionPercent > 0) && (
                  <StepRow 
                    step="6. Enemy Red" 
                    description="" 
                    minValue={afterDamageReduction} 
                    maxValue={afterDamageReduction}
                    formula={`(${formatNumber(Math.round(withAddDamage))} - ${enemyDamageReduction}) × ${(100 - enemyDamageReductionPercent)/100}`}
                  />
                )}

                <StepRow 
                  step="7. Final Mods" 
                  description="" 
                  minValue={finalBaseDamage} 
                  maxValue={finalBaseDamage}
                  formula={`${formatNumber(Math.round(afterDamageReduction))} × ${(100 + finalDamageIncreased)/100} × ${(100 - enemyFinalDamageDecrease)/100}`}
                />

                <StepRow 
                  step="8. Normal Up" 
                  description="" 
                  minValue={damageType === 'magic' ? finalBaseDamage * (100 + normalDamageUp) / 100 : finalBaseDamage * (100 + normalDamageUp) / 100} 
                  maxValue={damageType === 'magic' ? finalBaseDamage * (100 + normalDamageUp) / 100 : finalBaseDamage * (100 + normalDamageUp) / 100}
                  formula={`${formatNumber(Math.round(finalBaseDamage))} × ${(100 + normalDamageUp)/100}`}
                />

                {damageType === 'sword' && (
                  <StepRow 
                    step="9. Variance" 
                    description="" 
                    minValue={minNormalDamage} 
                    maxValue={maxNormalDamage}
                    formula={`${formatNumber(Math.round(finalBaseDamage * (100 + normalDamageUp) / 100))} × (0.85 to 1.15)`}
                    highlight={true}
                  />
                )}

                {damageType === 'magic' && (
                  <StepRow 
                    step="9. Final" 
                    description="" 
                    minValue={minNormalDamage} 
                    maxValue={maxNormalDamage}
                    formula={`No variance for magic`}
                    highlight={true}
                  />
                )}
              </div>
            </div>

            {/* Critical Hit Calculation */}
            <div className="glass-panel border-l-4 border-stat-offensive">
              <div className="p-4 pb-3 border-b border-border-light">
                <h3 className="text-lg font-semibold text-stat-offensive glow-text-sm">Critical Hit Step-by-Step</h3>
              </div>
              <div className="p-3">
                <div className="grid grid-cols-3 gap-3 py-2 px-3 bg-theme-light/30 rounded-md font-semibold text-xs text-gray-300 mb-2">
                  <div>Step</div>
                  <div>Calculation</div>
                  <div className="text-right">Result</div>
                </div>

                <StepRow 
                  step="1. Base" 
                  description="" 
                  minValue={baseDamage} 
                  maxValue={baseDamage}
                  formula={`${formatNumber(baseAttackValue)}`}
                />

                <StepRow 
                  step="2. Skill Amp" 
                  description="" 
                  minValue={amplifiedDamage} 
                  maxValue={amplifiedDamage}
                  formula={`${formatNumber(baseAttackValue)} × ${(100 + totalSkillAmp)/100}`}
                />

                <StepRow 
                  step="3. Level Penalty" 
                  description="" 
                  minValue={levelAdjustedDamage} 
                  maxValue={levelAdjustedDamage}
                  formula={levelDifference > 0 ? `${formatNumber(Math.round(amplifiedDamage))} × ${(100 - levelPenaltyPercent)/100}` : `No penalty (${playerLevel} vs ${enemyLevel})`}
                />

                {enemyDefense > 0 && (
                  <StepRow 
                    step="4a. Base Defense" 
                    description="" 
                    minValue={enemyDefense > 0 ? ((1 - (1 / (1 + enemyDefense / 1000))) * 100) : 0} 
                    maxValue={enemyDefense > 0 ? ((1 - (1 / (1 + enemyDefense / 1000))) * 100) : 0}
                    formula={`1 - 1/(1 + ${formatNumber(enemyDefense)}/1000) = ${((1 - (1 / (1 + enemyDefense / 1000))) * 100).toFixed(2)}%`}
                  />
                )}

                {enemyDefense > 0 && (enemyIgnorePenetration > 0 || cancelIgnorePenetration > 0) && (
                  <StepRow 
                    step="4b. Enemy Ignore Red" 
                    description="" 
                    minValue={effectiveEnemyIgnorePenetration} 
                    maxValue={effectiveEnemyIgnorePenetration}
                    formula={`max(0, ${formatNumber(enemyIgnorePenetration)} - ${formatNumber(cancelIgnorePenetration)}) = ${formatNumber(effectiveEnemyIgnorePenetration)}`}
                  />
                )}

                {enemyDefense > 0 && (basePenetration > 0 || effectiveEnemyIgnorePenetration > 0) && (
                  <StepRow 
                    step="4c. Effective Pen" 
                    description="" 
                    minValue={effectivePenetration} 
                    maxValue={effectivePenetration}
                    formula={`max(0, ${formatNumber(basePenetration)} - ${formatNumber(effectiveEnemyIgnorePenetration)}) = ${formatNumber(effectivePenetration)}`}
                  />
                )}

                {enemyDefense > 0 && effectivePenetration > 0 && (
                  <StepRow 
                    step="4d. Pen Reduction" 
                    description="" 
                    minValue={((effectivePenetration / enemyDefense) * 100)} 
                    maxValue={((effectivePenetration / enemyDefense) * 100)}
                    formula={`${formatNumber(effectivePenetration)}/${formatNumber(enemyDefense)} = ${((effectivePenetration / enemyDefense) * 100).toFixed(2)}% reduction`}
                  />
                )}

                <StepRow 
                  step="4e. Final Defense" 
                  description="" 
                  minValue={defenseAdjustedDamage} 
                  maxValue={defenseAdjustedDamage}
                  formula={enemyDefense > 0 ? `Final: ${(defenseReduction * 100).toFixed(2)}% → ${formatNumber(Math.round(levelAdjustedDamage))} × ${(1 - defenseReduction).toFixed(3)}` : `No defense`}
                />

                {addDamage > 0 && (
                  <StepRow 
                    step="5. Add DMG" 
                    description="" 
                    minValue={withAddDamage} 
                    maxValue={withAddDamage}
                    formula={`${formatNumber(Math.round(defenseAdjustedDamage))} + ${formatNumber(addDamage)}`}
                  />
                )}

                {(enemyDamageReduction > 0 || enemyDamageReductionPercent > 0) && (
                  <StepRow 
                    step="6. Enemy Red" 
                    description="" 
                    minValue={afterDamageReduction} 
                    maxValue={afterDamageReduction}
                    formula={`(${formatNumber(Math.round(withAddDamage))} - ${enemyDamageReduction}) × ${(100 - enemyDamageReductionPercent)/100}`}
                  />
                )}

                <StepRow 
                  step="7. Final Mods" 
                  description="" 
                  minValue={finalBaseDamage} 
                  maxValue={finalBaseDamage}
                  formula={`${formatNumber(Math.round(afterDamageReduction))} × ${(100 + finalDamageIncreased)/100} × ${(100 - enemyFinalDamageDecrease)/100}`}
                />

                <StepRow 
                  step="8. Crit DMG" 
                  description="" 
                  minValue={minCriticalDamage} 
                  maxValue={maxCriticalDamage}
                  formula={`${formatNumber(Math.round(finalBaseDamage))} × ${(100 + critDamage)/100}`}
                  highlight={true}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DamageCalculationModal;