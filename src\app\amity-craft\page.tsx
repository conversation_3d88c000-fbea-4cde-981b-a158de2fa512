import Link from 'next/link';

export default function AmityCraftCalculatorPage() {
  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <Link href="/chloe-calculators" className="text-blue-400 hover:text-blue-300 text-sm">
            ← Back to Chloe Calculators
          </Link>
          <h1 className="text-4xl font-bold mt-4 mb-4">Amity Craft Calculator</h1>
          <p className="text-gray-300">
            Calculate profit margins for Amity crafting and materials.
          </p>
        </div>
        
        <div className="bg-gray-800 p-8 rounded-lg">
          <div className="text-center">
            <h2 className="text-2xl font-semibold mb-4">Coming Soon</h2>
            <p className="text-gray-300 mb-6">
              The Amity craft calculator is currently under development. This will include:
            </p>
            <ul className="text-left max-w-md mx-auto space-y-2 text-gray-300">
              <li>• Amity crafting profit analysis</li>
              <li>• Material cost calculations</li>
              <li>• Amity point requirements</li>
              <li>• Shared price management</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}