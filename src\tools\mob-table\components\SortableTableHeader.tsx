'use client';

import React, { useState } from 'react';
import { SortDirection } from '../config/columns';

interface SortableTableHeaderProps {
  title: string;
  sortKey: string;
  currentSort: { key: string; direction: SortDirection } | null;
  onSort: (key: string) => void;
  sortable?: boolean;
  width?: string;
}

// Smart abbreviation mapping for long stat names
const getDisplayTitle = (title: string): { display: string; tooltip?: string } => {
  const abbreviations: Record<string, { display: string; tooltip: string }> = {
    'Attack Rate': { display: 'ATK Rate', tooltip: 'Attack Rate' },
    'Attack Count Amp': { display: 'ATK Count', tooltip: 'Attack Count Amplifier' },
    'Ignore Accuracy': { display: 'Ign. ACC', tooltip: 'Ignore Accuracy' },
    'Ignore DMG Reduction': { display: 'Ign. DMG Red.', tooltip: 'Ignore Damage Reduction' },
    'Ignore Penetration': { display: 'Ign. PEN', tooltip: 'Ignore Penetration' },
    'Absolute DMG': { display: 'Abs. DMG', tooltip: 'Absolute Damage' },
    'Defense Rate': { display: 'DEF Rate', tooltip: 'Defense Rate' },
    'DMG Reduction': { display: 'DMG Red.', tooltip: 'Damage Reduction' },
    'Resist Crit Rate': { display: 'Res. Crit Rate', tooltip: 'Resist Critical Rate' },
    'Resist Skill Amp': { display: 'Res. Skill Amp', tooltip: 'Resist Skill Amplifier' },
    'Resist Crit DMG': { display: 'Res. Crit DMG', tooltip: 'Resist Critical Damage' },
    'Resist Silence': { display: 'Res. Silence', tooltip: 'Resist Silence' },
    'Default Min ATK': { display: 'Def. Min ATK', tooltip: 'Default Skill Physical Attack (Minimum)' },
    'Default Max ATK': { display: 'Def. Max ATK', tooltip: 'Default Skill Physical Attack (Maximum)' },
    'Default Reach': { display: 'Def. Reach', tooltip: 'Default Skill Reach' },
    'Default Range': { display: 'Def. Range', tooltip: 'Default Skill Range' },
    'Default Interval': { display: 'Def. Interval', tooltip: 'Default Skill Interval' },
    'Special Min ATK': { display: 'Spc. Min ATK', tooltip: 'Special Skill Physical Attack (Minimum)' },
    'Special Max ATK': { display: 'Spc. Max ATK', tooltip: 'Special Skill Physical Attack (Maximum)' },
    'Special Reach': { display: 'Spc. Reach', tooltip: 'Special Skill Reach' },
    'Special Range': { display: 'Spc. Range', tooltip: 'Special Skill Range' },
    'Special Interval': { display: 'Spc. Interval', tooltip: 'Special Skill Interval' },
    'Default Stance': { display: 'Def. Stance', tooltip: 'Default Skill Stance' },
    'Special Stance': { display: 'Spc. Stance', tooltip: 'Special Skill Stance' },
    'Special Group': { display: 'Spc. Group', tooltip: 'Special Skill Group' },
    'HP Recharge': { display: 'HP Regen', tooltip: 'HP Recharge Rate' },
    'Chase Range': { display: 'Chase Rng.', tooltip: 'Chase Range' },
    'HP Dmg Prop': { display: 'HP DMG Prop', tooltip: 'HP Damage Proportion' },
    'Is World Boss': { display: 'World Boss', tooltip: 'Is World Boss' },
    'Dungeon ID': { display: 'Dungeon', tooltip: 'Dungeon ID' }
  };

  const abbrev = abbreviations[title];
  return abbrev || { display: title };
};

export const SortableTableHeader: React.FC<SortableTableHeaderProps> = ({
  title,
  sortKey,
  currentSort,
  onSort,
  sortable = true,
  width
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const isActive = currentSort?.key === sortKey;
  const direction = isActive ? currentSort.direction : null;
  const { display, tooltip } = getDisplayTitle(title);

  const handleClick = () => {
    if (sortable) {
      onSort(sortKey);
    }
  };

  return (
    <th 
      className={`px-2 py-3 text-xs font-medium uppercase tracking-wider transition-colors relative ${
        sortKey === 'name' ? 'text-left' : 'text-center'
      } ${
        isActive ? 'text-stat-offensive bg-component-card/20' : 'text-game-gold'
      } ${
        sortable ? 'cursor-pointer hover:bg-component-card/30' : ''
      }`}
      style={{ width, minWidth: width }}
      onClick={handleClick}
      onMouseEnter={() => tooltip && setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      <div className={`flex items-center gap-1 min-w-0 ${
        sortKey === 'name' ? 'justify-start' : 'justify-center'
      }`}>
        <span className={`leading-tight ${
          sortKey === 'name' ? 'text-left' : 'truncate text-center flex-1'
        }`} title={tooltip || title}>
          {display}
        </span>
        {sortable && (
          <div className="flex flex-col flex-shrink-0">
            {direction === 'asc' ? (
              // Show up arrow for ascending
              <svg className="w-3 h-3 text-stat-offensive" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
            ) : (
              // Show down arrow for descending
              <svg className="w-3 h-3 text-stat-offensive" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            )}
          </div>
        )}
      </div>

      {/* Tooltip */}
      {tooltip && showTooltip && (
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 px-2 py-1 bg-gray-900 text-white text-xs rounded shadow-lg z-50 whitespace-nowrap border border-gray-600">
          {tooltip}
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-b-gray-900"></div>
        </div>
      )}
    </th>
  );
};