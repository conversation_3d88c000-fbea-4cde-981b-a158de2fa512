'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import BuildSummary from '@/tools/build-planner/components/main-layout/BuildSummary';
import SystemsSidebar from '@/tools/build-planner/components/main-layout/SystemsSidebar';
import CombatStats from '@/tools/build-planner/components/main-layout/CombatStats';
import { PetSystem } from '@/tools/build-planner/systems/pet';
import HonorMedalSystem from '@/tools/build-planner/systems/honor-medal/HonorMedalSystem';
import { AchievementSystem } from '@/tools/build-planner/systems/achievement/AchievementSystem';
import EquipmentSystem from '@/tools/build-planner/systems/equipment-system/EquipmentSystem';
import { CostumeSystem } from '@/tools/build-planner/systems/costumes';
import StellarSystem from '@/tools/build-planner/systems/stellar-system/StellarSystem';
import EssenceRuneSystem from '@/tools/build-planner/systems/essence-rune/EssenceRuneSystem';
import KarmaRuneSystem from '@/tools/build-planner/systems/karma-rune/KarmaRuneSystem';
import { OverlordMasterySystem } from '@/tools/build-planner/systems/overlord-mastery';
import { OverlordMasteryStats } from '@/tools/build-planner/systems/overlord-mastery/components/OverlordMasteryStats';
import MythLevelSystem from '@/tools/build-planner/systems/myth-level/MythLevelSystem';
import BuffsPotions from '@/tools/build-planner/systems/buffs-potions';
import { ClassSystem } from '@/tools/build-planner/systems/class';
import { CollectionSystem } from '@/tools/build-planner/systems/collection';
import { PassiveSkillsSystem } from '@/tools/build-planner/systems/passive-skills';
import { ClassPassiveSkillsSystem } from '@/tools/build-planner/systems/class-passive-skills';
import { GoldMeritSystem } from '@/tools/build-planner/systems/gold-merit';
import { PlatinumMeritSystem } from '@/tools/build-planner/systems/platinum-merit';
import { ForceWingSystem } from '@/tools/build-planner/systems/force-wing/ForceWingSystem';
import { BattleConfigurationSystem } from '@/tools/build-planner/systems/battle-configuration';
import { ManualStatsSystem } from '@/tools/build-planner/systems/manual-stats';
import { DamageAnalysisSystem } from '@/tools/build-planner/systems/damage-analysis';
import StatRegistryInitializer from '@/tools/build-planner/utils/StatRegistryInitializer';

import { cn } from '@/tools/build-planner/lib/utils';
import { PROGRESSION_SYSTEMS } from '@/tools/build-planner/data/systems-config';
import ShareBuildButton from '@/components/ui/ShareBuildButton';
import LoadBuildFromURL from '@/components/ui/LoadBuildFromURL';
import BuildExportImportButtons from '@/components/ui/BuildExportImportButtons';
import AlphaTestingBanner from '@/components/ui/AlphaTestingBanner';

import { saveBuildToStorage, loadBuildFromStorage } from '@/utils/sharing/universal-build-serializer';
import { toast } from 'react-hot-toast';
import Link from 'next/link';

// Main content component
function BuildPlannerContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  
  // Get initial system from URL or default to 'equipment'
  const getValidSystemId = (systemId: string | null): string => {
    if (!systemId) return 'equipment';
    
    // Handle backward compatibility for old system IDs
    if (systemId === 'character') {
      return 'stat-distribution';
    }
    
    // Check if the system ID exists in our systems config
    const validSystem = PROGRESSION_SYSTEMS.find(system => system.id === systemId);
    return validSystem ? systemId : 'equipment';
  };
  
  const initialSystem = getValidSystemId(searchParams.get('system'));
  const [activeSystem, setActiveSystem] = useState(initialSystem);

  // Function to handle system changes and update URL
  const handleSystemChange = (systemId: string) => {
    setActiveSystem(systemId);
    
    // Update URL with new system parameter
    const newSearchParams = new URLSearchParams(searchParams.toString());
    newSearchParams.set('system', systemId);
    router.replace(`?${newSearchParams.toString()}`, { scroll: false });
  };

  // Sync state with URL changes (for browser back/forward navigation)
  useEffect(() => {
    const systemFromUrl = getValidSystemId(searchParams.get('system'));
    if (systemFromUrl !== activeSystem) {
      setActiveSystem(systemFromUrl);
    }
  }, [searchParams]); // Removed activeSystem from dependencies to prevent race condition

  // Auto-load saved build on page load
  useEffect(() => {
    const loadSavedBuild = () => {
      try {
        const result = loadBuildFromStorage();
        if (result.success) {
          // Build loaded successfully
        }
      } catch (error) {
        console.error('Error loading saved build:', error);
      }
    };

    loadSavedBuild();
  }, []);

  const handleSaveBuild = async () => {
    try {
      const result = saveBuildToStorage();
      
      if (result.success) {
        toast.success(`Build saved successfully! (${result.size} characters compressed)`);
      } else {
        toast.error(`Save failed: ${result.error}`);
      }
    } catch (error) {
      toast.error('Save failed: Unknown error');
      console.error('Save error:', error);
    }
  };

  const handleResetBuild = () => {
    try {
      // Clear all localStorage
      if (typeof window !== 'undefined') {
        localStorage.clear();
      }

      // Refresh the page to reset all state
      window.location.reload();
    } catch (error) {
      toast.error('Reset failed: Unknown error');
      console.error('Reset error:', error);
    }
  };

  return (
    <div className="text-gray-200">
      <div className="container mx-auto max-w-8xl p-2 sm:p-4 md:p-5 lg:p-6">
        {/* Initialize the Stat Registry */}
        <StatRegistryInitializer />
        
        {/* Load Build From URL Component */}
        <LoadBuildFromURL />
      
      {/* Header Section */}
      <div className="component-bg-dark mb-3 sm:mb-4 lg:mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 mb-2">
          <div className="flex items-center gap-4">
            <Link 
              href="/" 
              className="text-blue-400 hover:text-blue-300 text-sm"
            >
              ← Back to Tools
            </Link>
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-game-gold glow-text-lg">Build Planner</h1>
          </div>
          <div className="flex flex-wrap gap-2 glass-button-group p-1">
            <button 
              onClick={handleResetBuild}
              className="glass-button-blue text-white font-semibold py-1.5 px-3 sm:py-2 sm:px-4 rounded-lg transition duration-150 hover:glass-button-hover text-sm sm:text-base"
            >
              Reset
            </button>
            <button 
              onClick={handleSaveBuild}
              className="glass-button-green text-white font-semibold py-1.5 px-3 sm:py-2 sm:px-4 rounded-lg transition duration-150 hover:glass-button-hover text-sm sm:text-base"
            >
              Save Build
            </button>
            <BuildExportImportButtons />
            <ShareBuildButton currentSystem={activeSystem} />
          </div>
        </div>
      </div>

      {/* Main Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
        {/* Sidebar - Systems List */}
        <div className="lg:col-span-1 component-bg">
          <SystemsSidebar 
            activeSystem={activeSystem} 
            onSystemChange={handleSystemChange} 
          />
        </div>

        {/* Main Content Area */}
        <div className="lg:col-span-3 space-y-3 sm:space-y-4 lg:space-y-6 component-bg-light bg-pattern-grid">
          {/* Combat Stats */}
          <div className="mb-3 sm:mb-4 lg:mb-6">
            <CombatStats />
          </div>
          
          {/* Active System Content */} 
          <div className="glass-panel-dark shadow-game p-3 sm:p-4 lg:p-6 mb-3 sm:mb-4 lg:mb-6">
            <h2 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4 text-game-gold glow-text-sm">{PROGRESSION_SYSTEMS.find(system => system.id === activeSystem)?.name}</h2>
            <div className={cn(
              "min-h-64 sm:min-h-80 lg:min-h-96",
              // Full width for collection, overlord mastery, gold merit, achievements, passive skills, manual stats, and damage analysis systems, center everything else
              activeSystem === 'collection' || activeSystem === 'overlord-mastery' || activeSystem === 'gold-merit' || activeSystem === 'achievements' || activeSystem === 'passive-skills' || activeSystem === 'manual-stats' || activeSystem === 'damage-analysis'
                ? "w-full" 
                : "flex items-center justify-center"
            )}>
              {activeSystem === 'stat-distribution' && (
                <ClassSystem />
              )}
              {activeSystem === 'pet' && (
                <PetSystem />
              )}
              {activeSystem === 'equipment' && (
                <EquipmentSystem />
              )}
              {activeSystem === 'stellar-link' && (
                <StellarSystem />
              )}
              {activeSystem === 'honor-medal' && (
                <HonorMedalSystem />
              )}
              {activeSystem === 'costumes' && (
                <CostumeSystem />
              )}
              {activeSystem === 'gold-merit' && (
                <GoldMeritSystem />
              )}
              {activeSystem === 'platinum-merit' && (
                <PlatinumMeritSystem />
              )}
              {activeSystem === 'force-wing' && (
                <ForceWingSystem />
              )}
              {activeSystem === 'essence-runes' && (
                <EssenceRuneSystem />
              )}
              {activeSystem === 'karma-runes' && (
                <KarmaRuneSystem />
              )}
              {activeSystem === 'overlord-mastery' && (
                <div className="flex flex-col lg:flex-row gap-6">
                  <div className="flex-1">
                    <OverlordMasterySystem />
                  </div>
                  <div className="w-full lg:w-80">
                    <OverlordMasteryStats />
                  </div>
                </div>
              )}
              {activeSystem === 'achievements' && (
                <AchievementSystem />
              )}
              {activeSystem === 'collection' && (
                <CollectionSystem />
              )}
              {activeSystem === 'mythical-level' && (
                <MythLevelSystem />
              )}
              {activeSystem === 'buffs-potions' && (
                <BuffsPotions />
              )}
              {activeSystem === 'passive-skills' && (
                <PassiveSkillsSystem />
              )}
              {activeSystem === 'class-passive-skills' && (
                <ClassPassiveSkillsSystem />
              )}
              {activeSystem === 'damage-analysis' && (
                <DamageAnalysisSystem />
              )}
              {activeSystem === 'battle-configuration' && (
                <BattleConfigurationSystem />
              )}
              {activeSystem === 'manual-stats' && (
                <ManualStatsSystem />
              )}
            </div>
          </div>

          {/* Build Summary */}
          <BuildSummary />
        </div>
      </div>
      
      {/* Legal Disclaimer Footer */}
      <div className="bg-gray-800/50 border border-gray-600 rounded-lg p-3 mt-6 text-center text-sm text-gray-300">
        <span>Fan-made tool for educational purposes. Game assets © ESTsoft/ESTgames. </span>
        <a href="https://nipperlug.com/legal" className="text-blue-400 hover:text-blue-300 underline">
          Legal Info
        </a>
      </div>
      </div>

      {/* Alpha Testing Banner - Fixed popup at bottom */}
      <AlphaTestingBanner />
    </div>
  );
}

// Loading fallback component
function LoadingFallback() {
  return (
    <div className="min-h-screen text-foreground">
      <StatRegistryInitializer />
      <div className="container mx-auto p-4">
        <div className="flex animate-pulse">
          <div className="w-64 bg-gray-800 rounded-lg h-96 mr-4"></div>
          <div className="flex-1 bg-gray-800 rounded-lg h-96"></div>
        </div>
      </div>
    </div>
  );
}

// Main page component with Suspense wrapper
export default function BuildPlannerPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <BuildPlannerContent />
    </Suspense>
  );
}