import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import <PERSON>rip<PERSON> from "next/script";
import { Toaster } from "react-hot-toast";
import { SpeedInsights } from "@vercel/speed-insights/next";
import MainLayout from "../components/layout/MainLayout";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Nipperlug - Cabal Online Tools & Guides",
  description: "Comprehensive Cabal Online tools including build planner, tier lists, calculators, and more. Optimize your character builds and find game resources.",
  keywords: "Cabal Online, build planner, tier lists, calculators, mob table, character builder, build optimizer, equipment calculator, stats calculator, Cabal Online builds",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  },
  openGraph: {
    title: "Nipperlug - Cabal Online Tools & Guides",
    description: "Comprehensive Cabal Online tools including build planner, tier lists, calculators, and more. Optimize your character builds and find game resources.",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <head>
        <meta name="google-site-verification" content="SCLSo7AA-230J1z0PyRbkY6T6FlVCJ57FtkRUR95MpU" />
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.png" type="image/png" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="author" content="Nipperlug" />
        <meta name="language" content="English" />
        <meta name="revisit-after" content="7 days" />
        <link rel="canonical" href="https://nipperlug.com" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <MainLayout>
          {children}
        </MainLayout>
        
        {/* Vercel Speed Insights */}
        <SpeedInsights />
        
        {/* Userback Feedback Widget */}
        <Script
          id="userback-widget"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              window.Userback = window.Userback || {};
              Userback.access_token = "A-PAGLqYxSHk5pvprnSE2skUdkm";
              (function(d) {
                var s = d.createElement('script');
                s.async = true;
                s.src = 'https://static.userback.io/widget/v1.js';
                (d.head || d.body).appendChild(s);
              })(document);
            `
          }}
        />
      </body>
    </html>
  );
}
