/**
 * Essence Rune Data - Complete rune definitions ported from prototype
 * Contains all essence rune variants with their stats, costs, and materials
 * 
 * DUPLICATION NOTICE:
 * This file contains similar data structure to the karma rune data.
 * If making changes here, consider whether the same changes should be applied to:
 * src/tools/build-planner/systems/karma-rune/data/karmaRuneData.ts
 * 
 * Notable differences:
 * - Uses 'baseStatType' instead of 'statType' for the same concept
 */

export interface EssenceRune {
  id: string;
  name: string;
  tier: number;
  maxLevel: number;
  baseStatType: string;
  category: 'offensive' | 'defensive' | 'utility';
  description?: string;
  location: string;
  valuePerLevel: number[];
  apCost: number[];
  materials: Array<{
    level: number;
    name: string | null;
    quantity: number;
  }>;
}

// Complete essence runes data ported from prototype
export const essenceRunes: EssenceRune[] = 

[
  {
    "id": "hp",
    "name": "HP I",
    "tier": 1,
    "maxLevel": 15,
    "baseStatType": "hp",
    "category": "defensive",
    "description": "Increases maximum HP",
    "location": "Illusion Castle Under World, Officer's Support Cube (WExp), Essence Rune Cube (DP)",
    "valuePerLevel": [10, 27, 44, 61, 78, 95, 112, 129, 146, 165, 182, 199, 216, 233, 250],
    "apCost": [13, 15, 18, 22, 27, 33, 40, 48, 57, 67, 78, 90, 103, 117, 132],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core(High)", "quantity": 1 },
      { "level": 3, "name": "Upgrade Core(High)", "quantity": 1 },
      { "level": 4, "name": "Upgrade Core(High)", "quantity": 1 },
      { "level": 5, "name": "Upgrade Core(High)", "quantity": 2 },
      { "level": 6, "name": "Upgrade Core(High)", "quantity": 2 },
      { "level": 7, "name": "Upgrade Core(High)", "quantity": 2 },
      { "level": 8, "name": "Upgrade Core(High)", "quantity": 3 },
      { "level": 9, "name": "Upgrade Core(High)", "quantity": 3 },
      { "level": 10, "name": "Upgrade Core(High)", "quantity": 3 },
      { "level": 11, "name": "Upgrade Core(High)", "quantity": 4 },
      { "level": 12, "name": "Upgrade Core(High)", "quantity": 4 },
      { "level": 13, "name": "Upgrade Core(High)", "quantity": 4 },
      { "level": 14, "name": "Upgrade Core(High)", "quantity": 5 },
      { "level": 15, "name": "Upgrade Core(High)", "quantity": 5 }
    ]
  },
  {
    "id": "hp2",
    "name": "HP II",
    "tier": 2,
    "maxLevel": 20,
    "baseStatType": "hp",
    "category": "defensive",
    "description": "Increases maximum HP (Enhanced)",
    "location": "Illusion Castle: Underworld (Apocrypha)",
    "valuePerLevel": [10, 27, 44, 61, 78, 95, 112, 129, 146, 165, 182, 199, 216, 233, 250, 267, 284, 301, 318, 335],
    "apCost": [20, 30, 60, 90, 120, 150, 180, 210, 240, 270, 300, 330, 360, 390, 420, 450, 480, 510, 540, 570],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Chaos Core", "quantity": 1 },
      { "level": 3, "name": "Chaos Core", "quantity": 1 },
      { "level": 4, "name": "Chaos Core", "quantity": 1 },
      { "level": 5, "name": "Chaos Core", "quantity": 1 },
      { "level": 6, "name": "Chaos Core", "quantity": 3 },
      { "level": 7, "name": "Chaos Core", "quantity": 3 },
      { "level": 8, "name": "Chaos Core", "quantity": 3 },
      { "level": 9, "name": "Chaos Core", "quantity": 3 },
      { "level": 10, "name": "Chaos Core", "quantity": 3 },
      { "level": 11, "name": "Chaos Core", "quantity": 5 },
      { "level": 12, "name": "Chaos Core", "quantity": 5 },
      { "level": 13, "name": "Chaos Core", "quantity": 5 },
      { "level": 14, "name": "Chaos Core", "quantity": 5 },
      { "level": 15, "name": "Chaos Core", "quantity": 5 },
      { "level": 16, "name": "Chaos Core", "quantity": 7 },
      { "level": 17, "name": "Chaos Core", "quantity": 7 },
      { "level": 18, "name": "Chaos Core", "quantity": 7 },
      { "level": 19, "name": "Chaos Core", "quantity": 7 },
      { "level": 20, "name": "Chaos Core", "quantity": 7 }
    ]
  },
  {
    "id": "hp3",
    "name": "HP III",
    "tier": 3,
    "maxLevel": 30,
    "baseStatType": "hp",
    "category": "defensive",
    "description": "Increases maximum HP (Ultimate)",
    "location": "Purifier in the Woods",
    "valuePerLevel": [10, 27, 44, 61, 78, 95, 112, 129, 146, 165, 182, 199, 216, 233, 250, 267, 284, 301, 318, 335, 352, 369, 386, 403, 420, 437, 454, 471, 488, 500],
    "apCost": [40, 65, 90, 115, 140, 165, 190, 215, 240, 265, 290, 315, 340, 365, 390, 415, 440, 465, 490, 515, 533, 551, 569, 587, 605, 623, 641, 659, 677, 695],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 2 },
      { "level": 3, "name": "Divine Stone", "quantity": 2 },
      { "level": 4, "name": "Divine Stone", "quantity": 2 },
      { "level": 5, "name": "Divine Stone", "quantity": 2 },
      { "level": 6, "name": "Divine Stone", "quantity": 4 },
      { "level": 7, "name": "Divine Stone", "quantity": 4 },
      { "level": 8, "name": "Divine Stone", "quantity": 4 },
      { "level": 9, "name": "Divine Stone", "quantity": 4 },
      { "level": 10, "name": "Divine Stone", "quantity": 4 },
      { "level": 11, "name": "Divine Stone", "quantity": 6 },
      { "level": 12, "name": "Divine Stone", "quantity": 6 },
      { "level": 13, "name": "Divine Stone", "quantity": 6 },
      { "level": 14, "name": "Divine Stone", "quantity": 6 },
      { "level": 15, "name": "Divine Stone", "quantity": 6 },
      { "level": 16, "name": "Divine Stone", "quantity": 8 },
      { "level": 17, "name": "Divine Stone", "quantity": 8 },
      { "level": 18, "name": "Divine Stone", "quantity": 8 },
      { "level": 19, "name": "Divine Stone", "quantity": 8 },
      { "level": 20, "name": "Divine Stone", "quantity": 8 },
      { "level": 21, "name": "Divine Stone", "quantity": 10 },
      { "level": 22, "name": "Divine Stone", "quantity": 10 },
      { "level": 23, "name": "Divine Stone", "quantity": 10 },
      { "level": 24, "name": "Divine Stone", "quantity": 10 },
      { "level": 25, "name": "Divine Stone", "quantity": 10 },
      { "level": 26, "name": "Divine Stone", "quantity": 12 },
      { "level": 27, "name": "Divine Stone", "quantity": 12 },
      { "level": 28, "name": "Divine Stone", "quantity": 12 },
      { "level": 29, "name": "Divine Stone", "quantity": 12 },
      { "level": 30, "name": "Divine Stone", "quantity": 12 }
    ]
  },
  {
    "id": "defenseRate",
    "name": "Defense Rate I",
    "tier": 1,
    "maxLevel": 20,
    "baseStatType": "defenseRate",
    "category": "defensive",
    "description": "Increases defense rate",
    "location": "Lakeside, Forbidden Island, Tower of the Dead B3F, Forbidden Island (Awakening), Essence Rune Cube (DP), Officer's Support Cube (WExp)",
    "valuePerLevel": [15, 30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300],
    "apCost": [3, 4, 5, 7, 9, 12, 15, 19, 23, 28, 33, 39, 45, 52, 59, 67, 76, 86, 97, 109],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Force Core(Highest)", "quantity": 1 },
      { "level": 3, "name": "Force Core(Highest)", "quantity": 1 },
      { "level": 4, "name": "Force Core(Highest)", "quantity": 1 },
      { "level": 5, "name": "Force Core(Highest)", "quantity": 1 },
      { "level": 6, "name": "Force Core(Highest)", "quantity": 1 },
      { "level": 7, "name": "Force Core(Highest)", "quantity": 1 },
      { "level": 8, "name": "Force Core(Highest)", "quantity": 2 },
      { "level": 9, "name": "Force Core(Highest)", "quantity": 2 },
      { "level": 10, "name": "Force Core(Highest)", "quantity": 2 },
      { "level": 11, "name": "Force Core(Highest)", "quantity": 2 },
      { "level": 12, "name": "Force Core(Highest)", "quantity": 2 },
      { "level": 13, "name": "Force Core(Highest)", "quantity": 3 },
      { "level": 14, "name": "Force Core(Highest)", "quantity": 3 },
      { "level": 15, "name": "Force Core(Highest)", "quantity": 3 },
      { "level": 16, "name": "Force Core(Highest)", "quantity": 4 },
      { "level": 17, "name": "Force Core(Highest)", "quantity": 4 },
      { "level": 18, "name": "Force Core(Highest)", "quantity": 5 },
      { "level": 19, "name": "Force Core(Highest)", "quantity": 5 },
      { "level": 20, "name": "Force Core(Highest)", "quantity": 6 }
    ]
  },
  {
    "id": "defenseRate2",
    "name": "Defense Rate II",
    "tier": 2,
    "maxLevel": 20,
    "baseStatType": "defenseRate",
    "category": "defensive",
    "description": "Increases defense rate (Enhanced)",
    "location": "Exchange Shop at Secret Dealer Hiroalev",
    "valuePerLevel": [15, 30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300],
    "apCost": [3, 4, 5, 7, 9, 12, 15, 19, 23, 28, 33, 39, 45, 52, 59, 67, 76, 86, 97, 109],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Troglo's Golden Fruit", "quantity": 5 },
      { "level": 3, "name": "Troglo's Golden Fruit", "quantity": 10 },
      { "level": 4, "name": "Troglo's Golden Fruit", "quantity": 15 },
      { "level": 5, "name": "Troglo's Golden Fruit", "quantity": 20 },
      { "level": 6, "name": "Troglo's Golden Fruit", "quantity": 25 },
      { "level": 7, "name": "Troglo's Golden Fruit", "quantity": 30 },
      { "level": 8, "name": "Troglo's Golden Fruit", "quantity": 35 },
      { "level": 9, "name": "Troglo's Golden Fruit", "quantity": 40 },
      { "level": 10, "name": "Troglo's Golden Fruit", "quantity": 45 },
      { "level": 11, "name": "Troglo's Golden Fruit", "quantity": 50 },
      { "level": 12, "name": "Troglo's Golden Fruit", "quantity": 55 },
      { "level": 13, "name": "Troglo's Golden Fruit", "quantity": 60 },
      { "level": 14, "name": "Troglo's Golden Fruit", "quantity": 65 },
      { "level": 15, "name": "Troglo's Golden Fruit", "quantity": 70 },
      { "level": 16, "name": "Troglo's Golden Fruit", "quantity": 75 },
      { "level": 17, "name": "Troglo's Golden Fruit", "quantity": 80 },
      { "level": 18, "name": "Troglo's Golden Fruit", "quantity": 85 },
      { "level": 19, "name": "Troglo's Golden Fruit", "quantity": 90 },
      { "level": 20, "name": "Troglo's Golden Fruit", "quantity": 95 }
    ]
  },
  {
    "id": "attackRate",
    "name": "Attack Rate I",
    "tier": 1,
    "maxLevel": 20,
    "baseStatType": "attackRate",
    "category": "offensive",
    "description": "Increases attack rate",
    "location": "Lakeside, Forbidden Island (Awakening), Essence Rune Cube (DP), Officer's Support Cube (WExp)",
    "valuePerLevel": [15, 30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300],
    "apCost": [3, 4, 5, 7, 9, 12, 15, 19, 23, 28, 33, 39, 45, 52, 59, 67, 76, 86, 97, 109],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Force Core(Highest)", "quantity": 1 },
      { "level": 3, "name": "Force Core(Highest)", "quantity": 1 },
      { "level": 4, "name": "Force Core(Highest)", "quantity": 1 },
      { "level": 5, "name": "Force Core(Highest)", "quantity": 1 },
      { "level": 6, "name": "Force Core(Highest)", "quantity": 1 },
      { "level": 7, "name": "Force Core(Highest)", "quantity": 1 },
      { "level": 8, "name": "Force Core(Highest)", "quantity": 2 },
      { "level": 9, "name": "Force Core(Highest)", "quantity": 2 },
      { "level": 10, "name": "Force Core(Highest)", "quantity": 2 },
      { "level": 11, "name": "Force Core(Highest)", "quantity": 2 },
      { "level": 12, "name": "Force Core(Highest)", "quantity": 2 },
      { "level": 13, "name": "Force Core(Highest)", "quantity": 3 },
      { "level": 14, "name": "Force Core(Highest)", "quantity": 3 },
      { "level": 15, "name": "Force Core(Highest)", "quantity": 3 },
      { "level": 16, "name": "Force Core(Highest)", "quantity": 4 },
      { "level": 17, "name": "Force Core(Highest)", "quantity": 4 },
      { "level": 18, "name": "Force Core(Highest)", "quantity": 5 },
      { "level": 19, "name": "Force Core(Highest)", "quantity": 5 },
      { "level": 20, "name": "Force Core(Highest)", "quantity": 6 }
    ]
  },
  {
    "id": "attackRate2",
    "name": "Attack Rate II",
    "tier": 2,
    "maxLevel": 20,
    "baseStatType": "attackRate",
    "category": "offensive",
    "description": "Increases attack rate (Enhanced)",
    "location": "Exchange Shop at Secret Dealer Hiroalev",
    "valuePerLevel": [15, 30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300],
    "apCost": [3, 4, 5, 7, 9, 12, 15, 19, 23, 28, 33, 39, 45, 52, 59, 67, 76, 86, 97, 109],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Troglo's Golden Fruit", "quantity": 5 },
      { "level": 3, "name": "Troglo's Golden Fruit", "quantity": 10 },
      { "level": 4, "name": "Troglo's Golden Fruit", "quantity": 15 },
      { "level": 5, "name": "Troglo's Golden Fruit", "quantity": 20 },
      { "level": 6, "name": "Troglo's Golden Fruit", "quantity": 25 },
      { "level": 7, "name": "Troglo's Golden Fruit", "quantity": 30 },
      { "level": 8, "name": "Troglo's Golden Fruit", "quantity": 35 },
      { "level": 9, "name": "Troglo's Golden Fruit", "quantity": 40 },
      { "level": 10, "name": "Troglo's Golden Fruit", "quantity": 45 },
      { "level": 11, "name": "Troglo's Golden Fruit", "quantity": 50 },
      { "level": 12, "name": "Troglo's Golden Fruit", "quantity": 55 },
      { "level": 13, "name": "Troglo's Golden Fruit", "quantity": 60 },
      { "level": 14, "name": "Troglo's Golden Fruit", "quantity": 65 },
      { "level": 15, "name": "Troglo's Golden Fruit", "quantity": 70 },
      { "level": 16, "name": "Troglo's Golden Fruit", "quantity": 75 },
      { "level": 17, "name": "Troglo's Golden Fruit", "quantity": 80 },
      { "level": 18, "name": "Troglo's Golden Fruit", "quantity": 85 },
      { "level": 19, "name": "Troglo's Golden Fruit", "quantity": 90 },
      { "level": 20, "name": "Troglo's Golden Fruit", "quantity": 95 }
    ]
  },
  {
    "id": "critDmg",
    "name": "Critical DMG I",
    "tier": 1,
    "maxLevel": 5,
    "baseStatType": "critDamage",
    "category": "offensive",
    "description": "Increases critical damage",
    "location": "Illusion Castle Radiant Hall, Officer's Support Cube (WExp), Essense Rune Cube (DP)",
    "valuePerLevel": [1, 2, 3, 4, 5],
    "apCost": [246, 248, 251, 255, 260],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core (Highest)", "quantity": 4 },
      { "level": 3, "name": "Upgrade Core (Highest)", "quantity": 5 },
      { "level": 4, "name": "Upgrade Core (Highest)", "quantity": 6 },
      { "level": 5, "name": "Upgrade Core (Highest)", "quantity": 7 }
    ]
  },
  {
    "id": "critDmg2",
    "name": "Critical DMG II",
    "tier": 2,
    "maxLevel": 7,
    "baseStatType": "critDamage",
    "category": "offensive",
    "description": "Increases critical damage (Enhanced)",
    "location": "Edge of Phantom",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7],
    "apCost": [300, 350, 400, 450, 500, 550, 600],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 4 },
      { "level": 3, "name": "Divine Stone", "quantity": 5 },
      { "level": 4, "name": "Divine Stone", "quantity": 6 },
      { "level": 5, "name": "Divine Stone", "quantity": 7 },
      { "level": 6, "name": "Divine Stone", "quantity": 8 },
      { "level": 7, "name": "Divine Stone", "quantity": 10 }
    ]
  },
  {
    "id": "defense",
    "name": "Defense I",
    "tier": 1,
    "maxLevel": 15,
    "baseStatType": "defense",
    "category": "defensive",
    "description": "Increases defense",
    "location": "Forbidden Island (Awakened), Officer's Support Cube (WExp), Essense Rune Cube (DP)",
    "valuePerLevel": [3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45],
    "apCost": [26, 30, 36, 44, 55, 68, 83, 100, 119, 141, 165, 191, 219, 249, 281],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core (Highest)", "quantity": 1 },
      { "level": 3, "name": "Upgrade Core (Highest)", "quantity": 1 },
      { "level": 4, "name": "Upgrade Core (Highest)", "quantity": 1 },
      { "level": 5, "name": "Upgrade Core (Highest)", "quantity": 1 },
      { "level": 6, "name": "Upgrade Core (Highest)", "quantity": 1 },
      { "level": 7, "name": "Upgrade Core (Highest)", "quantity": 1 },
      { "level": 8, "name": "Upgrade Core (Highest)", "quantity": 1 },
      { "level": 9, "name": "Upgrade Core (Highest)", "quantity": 1 },
      { "level": 10, "name": "Upgrade Core (Highest)", "quantity": 1 },
      { "level": 11, "name": "Upgrade Core (Highest)", "quantity": 1 },
      { "level": 12, "name": "Upgrade Core (Highest)", "quantity": 2 },
      { "level": 13, "name": "Upgrade Core (Highest)", "quantity": 3 },
      { "level": 14, "name": "Upgrade Core (Highest)", "quantity": 4 },
      { "level": 15, "name": "Upgrade Core (Highest)", "quantity": 5 }
    ]
  },
  {
    "id": "defense2",
    "name": "Defense II",
    "tier": 2,
    "maxLevel": 20,
    "baseStatType": "defense",
    "category": "defensive",
    "description": "Increases defense (Enhanced)",
    "location": "Illusion Castle: Radiant Hall (Apocrypha)",
    "valuePerLevel": [3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60],
    "apCost": [20, 50, 80, 110, 140, 170, 200, 230, 260, 290, 320, 350, 380, 410, 440, 470, 500, 530, 560, 590],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 1 },
      { "level": 3, "name": "Divine Stone", "quantity": 1 },
      { "level": 4, "name": "Divine Stone", "quantity": 1 },
      { "level": 5, "name": "Divine Stone", "quantity": 1 },
      { "level": 6, "name": "Divine Stone", "quantity": 1 },
      { "level": 7, "name": "Divine Stone", "quantity": 1 },
      { "level": 8, "name": "Divine Stone", "quantity": 1 },
      { "level": 9, "name": "Divine Stone", "quantity": 1 },
      { "level": 10, "name": "Divine Stone", "quantity": 1 },
      { "level": 11, "name": "Divine Stone", "quantity": 2 },
      { "level": 12, "name": "Divine Stone", "quantity": 3 },
      { "level": 13, "name": "Divine Stone", "quantity": 4 },
      { "level": 14, "name": "Divine Stone", "quantity": 5 },
      { "level": 15, "name": "Divine Stone", "quantity": 7 },
      { "level": 16, "name": "Divine Stone", "quantity": 7 },
      { "level": 17, "name": "Divine Stone", "quantity": 7 },
      { "level": 18, "name": "Divine Stone", "quantity": 7 },
      { "level": 19, "name": "Divine Stone", "quantity": 7 },
      { "level": 20, "name": "Divine Stone", "quantity": 10 }
    ]
  },
  {
    "id": "defense3",
    "name": "Defense III",
    "tier": 3,
    "maxLevel": 20,
    "baseStatType": "defense",
    "category": "defensive",
    "description": "Increases defense (Master)",
    "location": "Tower of the Dead B4F",
    "valuePerLevel": [5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100],
    "apCost": [50, 80, 110, 140, 170, 200, 230, 260, 290, 320, 350, 380, 410, 440, 470, 510, 550, 590, 630, 670],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 7 },
      { "level": 3, "name": "Divine Stone", "quantity": 7 },
      { "level": 4, "name": "Divine Stone", "quantity": 7 },
      { "level": 5, "name": "Divine Stone", "quantity": 7 },
      { "level": 6, "name": "Divine Stone", "quantity": 14 },
      { "level": 7, "name": "Divine Stone", "quantity": 14 },
      { "level": 8, "name": "Divine Stone", "quantity": 14 },
      { "level": 9, "name": "Divine Stone", "quantity": 14 },
      { "level": 10, "name": "Divine Stone", "quantity": 14 },
      { "level": 11, "name": "Divine Stone", "quantity": 21 },
      { "level": 12, "name": "Divine Stone", "quantity": 21 },
      { "level": 13, "name": "Divine Stone", "quantity": 21 },
      { "level": 14, "name": "Divine Stone", "quantity": 21 },
      { "level": 15, "name": "Divine Stone", "quantity": 21 },
      { "level": 16, "name": "Divine Stone", "quantity": 28 },
      { "level": 17, "name": "Divine Stone", "quantity": 28 },
      { "level": 18, "name": "Divine Stone", "quantity": 28 },
      { "level": 19, "name": "Divine Stone", "quantity": 28 },
      { "level": 20, "name": "Divine Stone", "quantity": 28 }
    ]
  },
  {
    "id": "exp",
    "name": "EXP",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "exp",
    "category": "utility",
    "description": "Increases EXP gain",
    "location": "Lakeside, Forbidden Island (Awakening), Essence Rune Cube (DP)",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    "apCost": [1, 2, 3, 5, 7, 10, 13, 17, 21, 26],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Force Core (High)", "quantity": 1 },
      { "level": 3, "name": "Force Core (High)", "quantity": 1 },
      { "level": 4, "name": "Force Core (High)", "quantity": 1 },
      { "level": 5, "name": "Force Core (High)", "quantity": 1 },
      { "level": 6, "name": "Force Core (High)", "quantity": 2 },
      { "level": 7, "name": "Force Core (High)", "quantity": 2 },
      { "level": 8, "name": "Force Core (High)", "quantity": 2 },
      { "level": 9, "name": "Force Core (High)", "quantity": 2 },
      { "level": 10, "name": "Force Core (High)", "quantity": 3 }
    ]
  },
  {
    "id": "exp2",
    "name": "EXP II",
    "tier": 2,
    "maxLevel": 15,
    "baseStatType": "exp",
    "category": "utility",
    "description": "Increases EXP gain (Enhanced)",
    "location": "Exchange Shop at Secret Dealer Hirogley",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15],
    "apCost": [300, 310, 320, 330, 340, 350, 360, 370, 380, 390, 400, 410, 420, 430, 440],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core", "quantity": 1 },
      { "level": 3, "name": "Upgrade Core", "quantity": 1 },
      { "level": 4, "name": "Upgrade Core", "quantity": 1 },
      { "level": 5, "name": "Upgrade Core", "quantity": 1 },
      { "level": 6, "name": "Upgrade Core", "quantity": 1 },
      { "level": 7, "name": "Upgrade Core", "quantity": 1 },
      { "level": 8, "name": "Upgrade Core", "quantity": 1 },
      { "level": 9, "name": "Upgrade Core", "quantity": 1 },
      { "level": 10, "name": "Upgrade Core", "quantity": 1 },
      { "level": 11, "name": "Upgrade Core", "quantity": 1 },
      { "level": 12, "name": "Upgrade Core", "quantity": 1 },
      { "level": 13, "name": "Upgrade Core", "quantity": 1 },
      { "level": 14, "name": "Upgrade Core", "quantity": 1 },
      { "level": 15, "name": "Upgrade Core", "quantity": 1 }
    ]
  },
  {
    "id": "skillExp",
    "name": "Skill EXP",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "skillExp",
    "category": "utility",
    "description": "Increases Skill EXP gain",
    "location": "Phoenix Nest, Forgotten Temple B2F, Forbidden Island",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    "apCost": [1, 2, 3, 5, 7, 10, 13, 17, 21, 26],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Force Core (High)", "quantity": 1 },
      { "level": 3, "name": "Force Core (High)", "quantity": 1 },
      { "level": 4, "name": "Force Core (High)", "quantity": 1 },
      { "level": 5, "name": "Force Core (High)", "quantity": 1 },
      { "level": 6, "name": "Force Core (High)", "quantity": 2 },
      { "level": 7, "name": "Force Core (High)", "quantity": 2 },
      { "level": 8, "name": "Force Core (High)", "quantity": 2 },
      { "level": 9, "name": "Force Core (High)", "quantity": 2 },
      { "level": 10, "name": "Force Core (High)", "quantity": 3 }
    ]
  },
  {
    "id": "partyExp",
    "name": "Party EXP",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "partyExp",
    "category": "utility",
    "description": "Increases Party EXP gain",
    "location": "Tower of the Dead B2F, Forgotten Temple B2F, Forbidden Island",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    "apCost": [1, 2, 3, 5, 7, 10, 13, 17, 21, 26],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Force Core (High)", "quantity": 1 },
      { "level": 3, "name": "Force Core (High)", "quantity": 1 },
      { "level": 4, "name": "Force Core (High)", "quantity": 1 },
      { "level": 5, "name": "Force Core (High)", "quantity": 1 },
      { "level": 6, "name": "Force Core (High)", "quantity": 2 },
      { "level": 7, "name": "Force Core (High)", "quantity": 2 },
      { "level": 8, "name": "Force Core (High)", "quantity": 2 },
      { "level": 9, "name": "Force Core (High)", "quantity": 2 },
      { "level": 10, "name": "Force Core (High)", "quantity": 3 }
    ]
  },
  {
    "id": "petExp",
    "name": "Pet EXP",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "petExp",
    "category": "utility",
    "description": "Increases Pet EXP gain",
    "location": "Volcanic Citadel, Forgotten Temple B2F, Forbidden Island",
    "valuePerLevel": [5, 10, 15, 20, 25, 30, 35, 40, 45, 50],
    "apCost": [1, 2, 3, 5, 7, 10, 13, 17, 21, 26],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Force Core (High)", "quantity": 1 },
      { "level": 3, "name": "Force Core (High)", "quantity": 1 },
      { "level": 4, "name": "Force Core (High)", "quantity": 1 },
      { "level": 5, "name": "Force Core (High)", "quantity": 1 },
      { "level": 6, "name": "Force Core (High)", "quantity": 2 },
      { "level": 7, "name": "Force Core (High)", "quantity": 2 },
      { "level": 8, "name": "Force Core (High)", "quantity": 2 },
      { "level": 9, "name": "Force Core (High)", "quantity": 2 },
      { "level": 10, "name": "Force Core (High)", "quantity": 3 }
    ]
  },
  {
    "id": "alzDropAmount",
    "name": "Alz Drop Amount",
    "tier": 1,
    "maxLevel": 15,
    "baseStatType": "alzDropAmount",
    "category": "utility",
    "description": "Increases Alz Drop Amount",
    "location": "Pontus Ferrum, Porta Inferno, Arcane Trace",
    "valuePerLevel": [2, 4, 7, 9, 12, 14, 17, 19, 22, 24, 27, 29, 32, 34, 37],
    "apCost": [2, 2, 3, 4, 6, 8, 10, 13, 16, 20, 24, 28, 33, 38, 44],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Force Core (High)", "quantity": 1 },
      { "level": 3, "name": "Force Core (High)", "quantity": 1 },
      { "level": 4, "name": "Force Core (High)", "quantity": 1 },
      { "level": 5, "name": "Force Core (High)", "quantity": 1 },
      { "level": 6, "name": "Force Core (High)", "quantity": 1 },
      { "level": 7, "name": "Force Core (High)", "quantity": 1 },
      { "level": 8, "name": "Force Core (High)", "quantity": 2 },
      { "level": 9, "name": "Force Core (High)", "quantity": 2 },
      { "level": 10, "name": "Force Core (High)", "quantity": 2 },
      { "level": 11, "name": "Force Core (High)", "quantity": 2 },
      { "level": 12, "name": "Force Core (High)", "quantity": 2 },
      { "level": 13, "name": "Force Core (High)", "quantity": 3 },
      { "level": 14, "name": "Force Core (High)", "quantity": 3 },
      { "level": 15, "name": "Force Core (High)", "quantity": 3 }
    ]
  },
  {
    "id": "alzDropRate",
    "name": "Alz Drop Rate",
    "tier": 1,
    "maxLevel": 15,
    "baseStatType": "alzDropRate",
    "category": "utility",
    "description": "Increases Alz Drop Rate",
    "location": "Unknown - Data missing from original runeProgression",
    "valuePerLevel": [1, 2, 4, 5, 7, 8, 10, 11, 13, 14, 16, 17, 19, 20, 22],
    "apCost": [2, 2, 3, 4, 6, 8, 10, 13, 16, 20, 24, 28, 33, 38, 44],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Force Core (High)", "quantity": 1 },
      { "level": 3, "name": "Force Core (High)", "quantity": 1 },
      { "level": 4, "name": "Force Core (High)", "quantity": 1 },
      { "level": 5, "name": "Force Core (High)", "quantity": 1 },
      { "level": 6, "name": "Force Core (High)", "quantity": 1 },
      { "level": 7, "name": "Force Core (High)", "quantity": 1 },
      { "level": 8, "name": "Force Core (High)", "quantity": 2 },
      { "level": 9, "name": "Force Core (High)", "quantity": 2 },
      { "level": 10, "name": "Force Core (High)", "quantity": 2 },
      { "level": 11, "name": "Force Core (High)", "quantity": 2 },
      { "level": 12, "name": "Force Core (High)", "quantity": 2 },
      { "level": 13, "name": "Force Core (High)", "quantity": 2 },
      { "level": 14, "name": "Force Core (High)", "quantity": 3 },
      { "level": 15, "name": "Force Core (High)", "quantity": 3 }
    ]
  },
  {
    "id": "alzBombChance",
    "name": "Alz Bomb Chance",
    "tier": 1,
    "maxLevel": 15,
    "baseStatType": "alzBombChance",
    "category": "utility",
    "description": "Increases Alz Bomb Chance",
    "location": "Unknown - Data missing from original runeProgression",
    "valuePerLevel": [1, 2, 4, 5, 7, 8, 10, 11, 13, 15, 16, 17, 19, 20, 22],
    "apCost": [2, 2, 3, 4, 6, 8, 10, 13, 16, 20, 24, 28, 33, 38, 44],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Force Core (High)", "quantity": 1 },
      { "level": 3, "name": "Force Core (High)", "quantity": 1 },
      { "level": 4, "name": "Force Core (High)", "quantity": 1 },
      { "level": 5, "name": "Force Core (High)", "quantity": 1 },
      { "level": 6, "name": "Force Core (High)", "quantity": 1 },
      { "level": 7, "name": "Force Core (High)", "quantity": 1 },
      { "level": 8, "name": "Force Core (High)", "quantity": 2 },
      { "level": 9, "name": "Force Core (High)", "quantity": 2 },
      { "level": 10, "name": "Force Core (High)", "quantity": 2 },
      { "level": 11, "name": "Force Core (High)", "quantity": 2 },
      { "level": 12, "name": "Force Core (High)", "quantity": 2 },
      { "level": 13, "name": "Force Core (High)", "quantity": 2 },
      { "level": 14, "name": "Force Core (High)", "quantity": 3 },
      { "level": 15, "name": "Force Core (High)", "quantity": 3 }
    ]
  },
  {
    "id": "resistDown",
    "name": "Resist Down",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "resistDown",
    "category": "defensive",
    "description": "Increases Resist Down",
    "location": "Unknown - Data missing from original runeProgression",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    "apCost": [5, 14, 27, 45, 67, 94, 125, 163, 209, 264],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Force Core (Highest)", "quantity": 1 },
      { "level": 3, "name": "Force Core (Highest)", "quantity": 2 },
      { "level": 4, "name": "Force Core (Highest)", "quantity": 4 },
      { "level": 5, "name": "Force Core (Highest)", "quantity": 5 },
      { "level": 6, "name": "Force Core (Highest)", "quantity": 6 },
      { "level": 7, "name": "Force Core (Highest)", "quantity": 7 },
      { "level": 8, "name": "Force Core (Highest)", "quantity": 8 },
      { "level": 9, "name": "Force Core (Highest)", "quantity": 9 },
      { "level": 10, "name": "Force Core (Highest)", "quantity": 10 }
    ]
  },
  {
    "id": "resistKnockBack",
    "name": "Resist Knockback",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "resistKnockback",
    "category": "defensive",
    "description": "Increases Resist Knockback",
    "location": "Unknown - Data missing from original runeProgression",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    "apCost": [5, 14, 27, 45, 67, 94, 125, 163, 209, 264],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Force Core (Highest)", "quantity": 1 },
      { "level": 3, "name": "Force Core (Highest)", "quantity": 2 },
      { "level": 4, "name": "Force Core (Highest)", "quantity": 4 },
      { "level": 5, "name": "Force Core (Highest)", "quantity": 5 },
      { "level": 6, "name": "Force Core (Highest)", "quantity": 6 },
      { "level": 7, "name": "Force Core (Highest)", "quantity": 7 },
      { "level": 8, "name": "Force Core (Highest)", "quantity": 8 },
      { "level": 9, "name": "Force Core (Highest)", "quantity": 9 },
      { "level": 10, "name": "Force Core (Highest)", "quantity": 10 }
    ]
  },
  {
    "id": "resistStun",
    "name": "Resist Stun",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "resistStun",
    "category": "defensive",
    "description": "Increases Resist Stun",
    "location": "Forbidden Island (Awakened) Officer's Support Cube (WExp) Essence Rune Cube (DP)",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    "apCost": [5, 14, 27, 45, 67, 94, 125, 163, 209, 264],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Force Core (Highest)", "quantity": 1 },
      { "level": 3, "name": "Force Core (Highest)", "quantity": 2 },
      { "level": 4, "name": "Force Core (Highest)", "quantity": 4 },
      { "level": 5, "name": "Force Core (Highest)", "quantity": 5 },
      { "level": 6, "name": "Force Core (Highest)", "quantity": 6 },
      { "level": 7, "name": "Force Core (Highest)", "quantity": 7 },
      { "level": 8, "name": "Force Core (Highest)", "quantity": 8 },
      { "level": 9, "name": "Force Core (Highest)", "quantity": 9 },
      { "level": 10, "name": "Force Core (Highest)", "quantity": 10 }
    ]
  },
  {
    "id": "str",
    "name": "STR",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "str",
    "category": "offensive",
    "description": "Increases STR",
    "location": "Tower of Undead B1F Officer's Support Cube (WExp) Essence Rune Cube (DP)",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    "apCost": [8, 25, 50, 84, 126, 177, 236, 304, 382, 471],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core (High)", "quantity": 1 },
      { "level": 3, "name": "Upgrade Core (High)", "quantity": 2 },
      { "level": 4, "name": "Upgrade Core (High)", "quantity": 4 },
      { "level": 5, "name": "Upgrade Core (High)", "quantity": 4 },
      { "level": 6, "name": "Upgrade Core (High)", "quantity": 5 },
      { "level": 7, "name": "Upgrade Core (High)", "quantity": 6 },
      { "level": 8, "name": "Upgrade Core (High)", "quantity": 7 },
      { "level": 9, "name": "Upgrade Core (High)", "quantity": 8 },
      { "level": 10, "name": "Upgrade Core (High)", "quantity": 10 }
    ]
  },
  {
    "id": "str2",
    "name": "STR II",
    "tier": 2,
    "maxLevel": 20,
    "baseStatType": "str",
    "category": "offensive",
    "description": "Increases STR (Enhanced)",
    "location": "NPC Chloe's Request with material from Labyrinth",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
    "apCost": [20, 50, 80, 110, 140, 170, 200, 230, 260, 290, 320, 350, 380, 410, 440, 470, 500, 530, 560, 590],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 2 },
      { "level": 3, "name": "Divine Stone", "quantity": 2 },
      { "level": 4, "name": "Divine Stone", "quantity": 2 },
      { "level": 5, "name": "Divine Stone", "quantity": 2 },
      { "level": 6, "name": "Divine Stone", "quantity": 2 },
      { "level": 7, "name": "Divine Stone", "quantity": 2 },
      { "level": 8, "name": "Divine Stone", "quantity": 2 },
      { "level": 9, "name": "Divine Stone", "quantity": 2 },
      { "level": 10, "name": "Divine Stone", "quantity": 2 },
      { "level": 11, "name": "Divine Stone", "quantity": 4 },
      { "level": 12, "name": "Divine Stone", "quantity": 6 },
      { "level": 13, "name": "Divine Stone", "quantity": 8 },
      { "level": 14, "name": "Divine Stone", "quantity": 10 },
      { "level": 15, "name": "Divine Stone", "quantity": 14 },
      { "level": 16, "name": "Divine Stone", "quantity": 14 },
      { "level": 17, "name": "Divine Stone", "quantity": 14 },
      { "level": 18, "name": "Divine Stone", "quantity": 14 },
      { "level": 19, "name": "Divine Stone", "quantity": 14 },
      { "level": 20, "name": "Divine Stone", "quantity": 20 }
    ]
  },
  {
    "id": "dex",
    "name": "DEX",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "dex",
    "category": "offensive",
    "description": "Increases DEX",
    "location": "The Volcanic Citadel Officer's Support Cube (WExp) Essense Rune Cube (DP)",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    "apCost": [8, 25, 50, 84, 126, 177, 236, 304, 382, 471],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core (High)", "quantity": 1 },
      { "level": 3, "name": "Upgrade Core (High)", "quantity": 2 },
      { "level": 4, "name": "Upgrade Core (High)", "quantity": 4 },
      { "level": 5, "name": "Upgrade Core (High)", "quantity": 4 },
      { "level": 6, "name": "Upgrade Core (High)", "quantity": 5 },
      { "level": 7, "name": "Upgrade Core (High)", "quantity": 6 },
      { "level": 8, "name": "Upgrade Core (High)", "quantity": 7 },
      { "level": 9, "name": "Upgrade Core (High)", "quantity": 8 },
      { "level": 10, "name": "Upgrade Core (High)", "quantity": 10 }
    ]
  },
  {
    "id": "dex2",
    "name": "DEX II",
    "tier": 2,
    "maxLevel": 20,
    "baseStatType": "dex",
    "category": "offensive",
    "description": "Increases DEX (Enhanced)",
    "location": "NPC Chloe's Request with material from Labyrinth",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
    "apCost": [20, 50, 80, 110, 140, 170, 200, 230, 260, 290, 320, 350, 380, 410, 440, 470, 500, 530, 560, 590],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 2 },
      { "level": 3, "name": "Divine Stone", "quantity": 2 },
      { "level": 4, "name": "Divine Stone", "quantity": 2 },
      { "level": 5, "name": "Divine Stone", "quantity": 2 },
      { "level": 6, "name": "Divine Stone", "quantity": 2 },
      { "level": 7, "name": "Divine Stone", "quantity": 2 },
      { "level": 8, "name": "Divine Stone", "quantity": 2 },
      { "level": 9, "name": "Divine Stone", "quantity": 2 },
      { "level": 10, "name": "Divine Stone", "quantity": 2 },
      { "level": 11, "name": "Divine Stone", "quantity": 4 },
      { "level": 12, "name": "Divine Stone", "quantity": 6 },
      { "level": 13, "name": "Divine Stone", "quantity": 8 },
      { "level": 14, "name": "Divine Stone", "quantity": 10 },
      { "level": 15, "name": "Divine Stone", "quantity": 14 },
      { "level": 16, "name": "Divine Stone", "quantity": 14 },
      { "level": 17, "name": "Divine Stone", "quantity": 14 },
      { "level": 18, "name": "Divine Stone", "quantity": 14 },
      { "level": 19, "name": "Divine Stone", "quantity": 14 },
      { "level": 20, "name": "Divine Stone", "quantity": 20 }
    ]
  },
  {
    "id": "int",
    "name": "INT",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "int",
    "category": "offensive",
    "description": "Increases INT",
    "location": "Tower of Undead B2F Officer's Support Cube (WExp) Essense Rune Cube (DP)",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    "apCost": [8, 25, 50, 84, 126, 177, 236, 304, 382, 471],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core (High)", "quantity": 1 },
      { "level": 3, "name": "Upgrade Core (High)", "quantity": 2 },
      { "level": 4, "name": "Upgrade Core (High)", "quantity": 4 },
      { "level": 5, "name": "Upgrade Core (High)", "quantity": 4 },
      { "level": 6, "name": "Upgrade Core (High)", "quantity": 5 },
      { "level": 7, "name": "Upgrade Core (High)", "quantity": 6 },
      { "level": 8, "name": "Upgrade Core (High)", "quantity": 7 },
      { "level": 9, "name": "Upgrade Core (High)", "quantity": 8 },
      { "level": 10, "name": "Upgrade Core (High)", "quantity": 10 }
    ]
  },
  {
    "id": "int2",
    "name": "INT II",
    "tier": 2,
    "maxLevel": 20,
    "baseStatType": "int",
    "category": "offensive",
    "description": "Increases INT (Enhanced)",
    "location": "NPC Chloe's Request with material from Labyrinth",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
    "apCost": [20, 50, 80, 110, 140, 170, 200, 230, 260, 290, 320, 350, 380, 410, 440, 470, 500, 530, 560, 590],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 2 },
      { "level": 3, "name": "Divine Stone", "quantity": 2 },
      { "level": 4, "name": "Divine Stone", "quantity": 2 },
      { "level": 5, "name": "Divine Stone", "quantity": 2 },
      { "level": 6, "name": "Divine Stone", "quantity": 2 },
      { "level": 7, "name": "Divine Stone", "quantity": 2 },
      { "level": 8, "name": "Divine Stone", "quantity": 2 },
      { "level": 9, "name": "Divine Stone", "quantity": 2 },
      { "level": 10, "name": "Divine Stone", "quantity": 2 },
      { "level": 11, "name": "Divine Stone", "quantity": 4 },
      { "level": 12, "name": "Divine Stone", "quantity": 6 },
      { "level": 13, "name": "Divine Stone", "quantity": 8 },
      { "level": 14, "name": "Divine Stone", "quantity": 10 },
      { "level": 15, "name": "Divine Stone", "quantity": 14 },
      { "level": 16, "name": "Divine Stone", "quantity": 14 },
      { "level": 17, "name": "Divine Stone", "quantity": 14 },
      { "level": 18, "name": "Divine Stone", "quantity": 14 },
      { "level": 19, "name": "Divine Stone", "quantity": 14 },
      { "level": 20, "name": "Divine Stone", "quantity": 20 }
    ]
  },
  {
    "id": "mp",
    "name": "MP",
    "tier": 1,
    "maxLevel": 15,
    "baseStatType": "mp",
    "category": "defensive",
    "description": "Increases MP",
    "location": "Forbidden Island (Awakened) Officer's Support Cube (WExp) Essense Rune Cube (DP)",
    "valuePerLevel": [10, 27, 44, 61, 78, 95, 112, 129, 146, 165, 182, 199, 216, 233, 250],
    "apCost": [13, 15, 18, 22, 27, 33, 40, 48, 57, 67, 78, 90, 103, 117, 132],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core (High)", "quantity": 1 },
      { "level": 3, "name": "Upgrade Core (High)", "quantity": 1 },
      { "level": 4, "name": "Upgrade Core (High)", "quantity": 1 },
      { "level": 5, "name": "Upgrade Core (High)", "quantity": 2 },
      { "level": 6, "name": "Upgrade Core (High)", "quantity": 2 },
      { "level": 7, "name": "Upgrade Core (High)", "quantity": 2 },
      { "level": 8, "name": "Upgrade Core (High)", "quantity": 3 },
      { "level": 9, "name": "Upgrade Core (High)", "quantity": 3 },
      { "level": 10, "name": "Upgrade Core (High)", "quantity": 3 },
      { "level": 11, "name": "Upgrade Core (High)", "quantity": 4 },
      { "level": 12, "name": "Upgrade Core (High)", "quantity": 4 },
      { "level": 13, "name": "Upgrade Core (High)", "quantity": 4 },
      { "level": 14, "name": "Upgrade Core (High)", "quantity": 5 },
      { "level": 15, "name": "Upgrade Core (High)", "quantity": 5 }
    ]
  },
  {
    "id": "attack",
    "name": "Attack",
    "tier": 1,
    "maxLevel": 15,
    "baseStatType": "attack",
    "category": "offensive",
    "description": "Increases attack power",
    "location": "Altar of Siena B1F, Officer's Support Cube (WExp), Essence Rune Cube (DP)",
    "valuePerLevel": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30],
    "apCost": [26, 30, 36, 44, 55, 68, 83, 100, 119, 141, 165, 191, 219, 249, 281],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 3, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 4, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 5, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 6, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 7, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 8, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 9, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 10, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 11, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 12, "name": "Upgrade Core(Highest)", "quantity": 2 },
      { "level": 13, "name": "Upgrade Core(Highest)", "quantity": 3 },
      { "level": 14, "name": "Upgrade Core(Highest)", "quantity": 4 },
      { "level": 15, "name": "Upgrade Core(Highest)", "quantity": 5 }
    ]
  },
  {
    "id": "attack2",
    "name": "Attack II",
    "tier": 2,
    "maxLevel": 20,
    "baseStatType": "attack",
    "category": "offensive",
    "description": "Increases attack power (Enhanced)",
    "location": "Mirage Island",
    "valuePerLevel": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40],
    "apCost": [20, 50, 80, 110, 140, 170, 200, 230, 260, 290, 320, 350, 380, 410, 440, 470, 500, 530, 560, 590],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 1 },
      { "level": 3, "name": "Divine Stone", "quantity": 1 },
      { "level": 4, "name": "Divine Stone", "quantity": 1 },
      { "level": 5, "name": "Divine Stone", "quantity": 1 },
      { "level": 6, "name": "Divine Stone", "quantity": 1 },
      { "level": 7, "name": "Divine Stone", "quantity": 1 },
      { "level": 8, "name": "Divine Stone", "quantity": 1 },
      { "level": 9, "name": "Divine Stone", "quantity": 1 },
      { "level": 10, "name": "Divine Stone", "quantity": 1 },
      { "level": 11, "name": "Divine Stone", "quantity": 2 },
      { "level": 12, "name": "Divine Stone", "quantity": 3 },
      { "level": 13, "name": "Divine Stone", "quantity": 4 },
      { "level": 14, "name": "Divine Stone", "quantity": 5 },
      { "level": 15, "name": "Divine Stone", "quantity": 7 },
      { "level": 16, "name": "Divine Stone", "quantity": 7 },
      { "level": 17, "name": "Divine Stone", "quantity": 7 },
      { "level": 18, "name": "Divine Stone", "quantity": 7 },
      { "level": 19, "name": "Divine Stone", "quantity": 7 },
      { "level": 20, "name": "Divine Stone", "quantity": 10 }
    ]
  },
  {
    "id": "magicAttack",
    "name": "Magic Attack",
    "tier": 1,
    "maxLevel": 15,
    "baseStatType": "magicAttack",
    "category": "offensive",
    "description": "Increases magic attack power",
    "location": "Altar of Siena B1F, Officer's Support Cube (WExp), Essence Rune Cube (DP)",
    "valuePerLevel": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30],
    "apCost": [26, 30, 36, 44, 55, 68, 83, 100, 119, 141, 165, 191, 219, 249, 281],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 3, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 4, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 5, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 6, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 7, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 8, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 9, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 10, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 11, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 12, "name": "Upgrade Core(Highest)", "quantity": 2 },
      { "level": 13, "name": "Upgrade Core(Highest)", "quantity": 3 },
      { "level": 14, "name": "Upgrade Core(Highest)", "quantity": 4 },
      { "level": 15, "name": "Upgrade Core(Highest)", "quantity": 5 }
    ]
  },
  {
    "id": "magicAttack2",
    "name": "Magic Attack II",
    "tier": 2,
    "maxLevel": 20,
    "baseStatType": "magicAttack",
    "category": "offensive",
    "description": "Increases Magic Attack (Enhanced)",
    "location": "Mirage Island",
    "valuePerLevel": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40],
    "apCost": [20, 50, 80, 110, 140, 170, 200, 230, 260, 290, 320, 350, 380, 410, 440, 470, 500, 530, 560, 590],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 1 },
      { "level": 3, "name": "Divine Stone", "quantity": 1 },
      { "level": 4, "name": "Divine Stone", "quantity": 1 },
      { "level": 5, "name": "Divine Stone", "quantity": 1 },
      { "level": 6, "name": "Divine Stone", "quantity": 1 },
      { "level": 7, "name": "Divine Stone", "quantity": 1 },
      { "level": 8, "name": "Divine Stone", "quantity": 1 },
      { "level": 9, "name": "Divine Stone", "quantity": 1 },
      { "level": 10, "name": "Divine Stone", "quantity": 1 },
      { "level": 11, "name": "Divine Stone", "quantity": 2 },
      { "level": 12, "name": "Divine Stone", "quantity": 3 },
      { "level": 13, "name": "Divine Stone", "quantity": 4 },
      { "level": 14, "name": "Divine Stone", "quantity": 5 },
      { "level": 15, "name": "Divine Stone", "quantity": 7 },
      { "level": 16, "name": "Divine Stone", "quantity": 7 },
      { "level": 17, "name": "Divine Stone", "quantity": 7 },
      { "level": 18, "name": "Divine Stone", "quantity": 7 },
      { "level": 19, "name": "Divine Stone", "quantity": 7 },
      { "level": 20, "name": "Divine Stone", "quantity": 10 }
    ]
  },
  {
    "id": "maxHpSteal",
    "name": "Max HP Absorb Up",
    "tier": 1,
    "maxLevel": 15,
    "baseStatType": "maxHpSteal",
    "category": "utility",
    "description": "Increases Max HP Absorb",
    "location": "Forgotten Temple B1F, Officer's Support Cube (WExp), Essence Rune Cube (DP)",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15],
    "apCost": [30, 31, 33, 36, 40, 45, 51, 58, 66, 75, 85, 96, 108, 121, 135],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 3, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 4, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 5, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 6, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 7, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 8, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 9, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 10, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 11, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 12, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 13, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 14, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 15, "name": "Upgrade Core(Highest)", "quantity": 2 }
    ]
  },
  {
    "id": "maxMpSteal",
    "name": "Max MP Absorb Up",
    "tier": 1,
    "maxLevel": 15,
    "baseStatType": "maxMpSteal",
    "category": "utility",
    "description": "Increases Max MP Absorb",
    "location": "Forgotten Temple B1F, Officer's Support Cube (WExp), Essence Rune Cube (DP)",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15],
    "apCost": [19, 20, 22, 25, 29, 34, 40, 47, 55, 64, 73, 83, 94, 106, 119],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 3, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 4, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 5, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 6, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 7, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 8, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 9, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 10, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 11, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 12, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 13, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 14, "name": "Upgrade Core(Highest)", "quantity": 1 },
      { "level": 15, "name": "Upgrade Core(Highest)", "quantity": 2 }
    ]
  },
  {
    "id": "hpAbsorb",
    "name": "HP Absorb Up",
    "tier": 1,
    "maxLevel": 3,
    "baseStatType": "hpAbsorb",
    "category": "utility",
    "description": "Increases HP Absorb",
    "location": "Forbidden Island, Officer's Support Cube (WExp), Essence Rune Cube (DP)",
    "valuePerLevel": [1, 2, 3],
    "apCost": [240, 324, 450],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core(Highest)", "quantity": 5 },
      { "level": 3, "name": "Upgrade Core(Highest)", "quantity": 10 }
    ]
  },
  {
    "id": "mpAbsorb",
    "name": "MP Absorb Up",
    "tier": 1,
    "maxLevel": 3,
    "baseStatType": "mpAbsorb",
    "category": "utility",
    "description": "Increases MP Absorb",
    "location": "Forbidden Island (Awakened), Officer's Support Cube (WExp), Essence Rune Cube (DP)",
    "valuePerLevel": [1, 2, 3],
    "apCost": [150, 234, 360],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core(Highest)", "quantity": 5 },
      { "level": 3, "name": "Upgrade Core(Highest)", "quantity": 10 }
    ]
  },
  {
    "id": "swordSkillAmp",
    "name": "Sword Skill Amp.",
    "tier": 1,
    "maxLevel": 4,
    "baseStatType": "swordSkillAmp",
    "category": "offensive",
    "description": "Increases Sword Skill Amp",
    "location": "Forgotten Temple B2F, Officer's Support Cube (WExp), Essence Rune Cube (DP)",
    "valuePerLevel": [1, 2, 3, 4],
    "apCost": [310, 312, 315, 320],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core(Highest)", "quantity": 3 },
      { "level": 3, "name": "Upgrade Core(Highest)", "quantity": 5 },
      { "level": 4, "name": "Upgrade Core(Highest)", "quantity": 10 }
    ]
  },
  {
    "id": "magicSkillAmp",
    "name": "Magic Skill Amp.",
    "tier": 1,
    "maxLevel": 4,
    "baseStatType": "magicSkillAmp",
    "category": "offensive",
    "description": "Increases Magic Skill Amp",
    "location": "Forgotten Temple B2F, Officer's Support Cube (WExp), Essence Rune Cube (DP)",
    "valuePerLevel": [1, 2, 3, 4],
    "apCost": [310, 312, 315, 320],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core(Highest)", "quantity": 3 },
      { "level": 3, "name": "Upgrade Core(Highest)", "quantity": 5 },
      { "level": 4, "name": "Upgrade Core(Highest)", "quantity": 10 }
    ]
  },
  {
    "id": "ignorePenetration1",
    "name": "Ignore Penetration I",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "ignorePenetration",
    "category": "defensive",
    "description": "Increases Ignore Penetration",
    "location": "Tower of the Dead B3F (Part 2)",
    "valuePerLevel": [8, 16, 24, 32, 40, 48, 56, 64, 72, 80],
    "apCost": [80, 97, 116, 138, 164, 195, 232, 276, 328, 389],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core (Highest)", "quantity": 3 },
      { "level": 3, "name": "Upgrade Core (Highest)", "quantity": 9 },
      { "level": 4, "name": "Upgrade Core (Highest)", "quantity": 12 },
      { "level": 5, "name": "Upgrade Core (Highest)", "quantity": 15 },
      { "level": 6, "name": "Upgrade Core (Highest)", "quantity": 18 },
      { "level": 7, "name": "Upgrade Core (Highest)", "quantity": 21 },
      { "level": 8, "name": "Upgrade Core (Highest)", "quantity": 24 },
      { "level": 9, "name": "Upgrade Core (Highest)", "quantity": 27 },
      { "level": 10, "name": "Upgrade Core (Highest)", "quantity": 30 }
    ]
  },
  {
    "id": "ignorePenetration2",
    "name": "Ignore Penetration II",
    "tier": 2,
    "maxLevel": 10,
    "baseStatType": "ignorePenetration",
    "category": "defensive",
    "description": "Increases Ignore Penetration (Enhanced)",
    "location": "Celestia",
    "valuePerLevel": [13, 21, 29, 37, 45, 53, 61, 69, 77, 85],
    "apCost": [450, 475, 500, 525, 550, 575, 600, 625, 650, 675],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 5 },
      { "level": 3, "name": "Divine Stone", "quantity": 5 },
      { "level": 4, "name": "Divine Stone", "quantity": 5 },
      { "level": 5, "name": "Divine Stone", "quantity": 10 },
      { "level": 6, "name": "Divine Stone", "quantity": 10 },
      { "level": 7, "name": "Divine Stone", "quantity": 10 },
      { "level": 8, "name": "Divine Stone", "quantity": 15 },
      { "level": 9, "name": "Divine Stone", "quantity": 20 },
      { "level": 10, "name": "Divine Stone", "quantity": 20 }
    ]
  },
  {
    "id": "accuracy",
    "name": "Accuracy",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "accuracy",
    "category": "offensive",
    "description": "Increases Accuracy",
    "location": "Tower of the Dead B3F, Essence Rune Cube (DP)",
    "valuePerLevel": [20, 40, 60, 80, 100, 120, 140, 160, 180, 200],
    "apCost": [80, 97, 116, 138, 164, 195, 232, 276, 328, 389],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core(Highest)", "quantity": 3 },
      { "level": 3, "name": "Upgrade Core(Highest)", "quantity": 9 },
      { "level": 4, "name": "Upgrade Core(Highest)", "quantity": 12 },
      { "level": 5, "name": "Upgrade Core(Highest)", "quantity": 15 },
      { "level": 6, "name": "Upgrade Core(Highest)", "quantity": 18 },
      { "level": 7, "name": "Upgrade Core(Highest)", "quantity": 21 },
      { "level": 8, "name": "Upgrade Core(Highest)", "quantity": 24 },
      { "level": 9, "name": "Upgrade Core(Highest)", "quantity": 27 },
      { "level": 10, "name": "Upgrade Core(Highest)", "quantity": 30 }
    ]
  },
  {
    "id": "penetration",
    "name": "Penetration",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "penetration",
    "category": "offensive",
    "description": "Increases Penetration",
    "location": "Abandoned City",
    "valuePerLevel": [4, 8, 12, 16, 20, 24, 28, 32, 36, 40],
    "apCost": [80, 100, 150, 200, 250, 300, 350, 400, 500, 600],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core(Highest)", "quantity": 3 },
      { "level": 3, "name": "Upgrade Core(Highest)", "quantity": 9 },
      { "level": 4, "name": "Upgrade Core(Highest)", "quantity": 12 },
      { "level": 5, "name": "Upgrade Core(Highest)", "quantity": 15 },
      { "level": 6, "name": "Upgrade Core(Highest)", "quantity": 18 },
      { "level": 7, "name": "Upgrade Core(Highest)", "quantity": 21 },
      { "level": 8, "name": "Upgrade Core(Highest)", "quantity": 24 },
      { "level": 9, "name": "Upgrade Core(Highest)", "quantity": 27 },
      { "level": 10, "name": "Upgrade Core(Highest)", "quantity": 30 }
    ]
  },
  {
    "id": "penetration2",
    "name": "Penetration II",
    "tier": 2,
    "maxLevel": 9,
    "baseStatType": "penetration",
    "category": "offensive",
    "description": "Increases Penetration (Enhanced)",
    "location": "Secret Base SCA-76",
    "valuePerLevel": [5, 10, 15, 20, 25, 30, 35, 40, 45],
    "apCost": [450, 475, 500, 525, 550, 575, 600, 625, 650],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 5 },
      { "level": 3, "name": "Divine Stone", "quantity": 5 },
      { "level": 4, "name": "Divine Stone", "quantity": 5 },
      { "level": 5, "name": "Divine Stone", "quantity": 10 },
      { "level": 6, "name": "Divine Stone", "quantity": 10 },
      { "level": 7, "name": "Divine Stone", "quantity": 10 },
      { "level": 8, "name": "Divine Stone", "quantity": 15 },
      { "level": 9, "name": "Divine Stone", "quantity": 20 }
    ]
  },
  {
    "id": "dmgReduction2",
    "name": "DMG Reduction II",
    "tier": 2,
    "maxLevel": 5,
    "baseStatType": "damageReduce",
    "category": "defensive",
    "description": "Increases Damage Reduction (Enhanced)",
    "location": "Garden of Dust",
    "valuePerLevel": [12, 24, 36, 48, 60],
    "apCost": [310, 312, 315, 320, 325],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 5 },
      { "level": 3, "name": "Divine Stone", "quantity": 10 },
      { "level": 4, "name": "Divine Stone", "quantity": 15 },
      { "level": 5, "name": "Divine Stone", "quantity": 20 }
    ]
  },
  {
    "id": "maxCriticalRate",
    "name": "Max Critical Rate Up",
    "tier": 1,
    "maxLevel": 3,
    "baseStatType": "maxCritRate",
    "category": "offensive",
    "description": "Increases Max Critical Rate",
    "location": "Altar of Siena B2F, Officer's Support Cube (WExp), Essence Rune Cube (DP)",
    "valuePerLevel": [1, 2, 3],
    "apCost": [352, 410, 497],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core(Highest)", "quantity": 8 },
      { "level": 3, "name": "Upgrade Core(Highest)", "quantity": 14 }
    ]
  },
  {
    "id": "dmgReduction",
    "name": "Damage Reduction",
    "tier": 1,
    "maxLevel": 5,
    "baseStatType": "damageReduce",
    "category": "defensive",
    "description": "Increases Damage Reduction",
    "location": "Maquinas Outpost, Essence Rune Cube (DP)",
    "valuePerLevel": [10, 20, 30, 40, 50],
    "apCost": [251, 302, 364, 437, 521],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core(Highest)", "quantity": 4 },
      { "level": 3, "name": "Upgrade Core(Highest)", "quantity": 6 },
      { "level": 4, "name": "Upgrade Core(Highest)", "quantity": 8 },
      { "level": 5, "name": "Upgrade Core(Highest)", "quantity": 10 }
    ]
  },
  {
    "id": "resistCritRate",
    "name": "Resist Critical Rate",
    "tier": 1,
    "maxLevel": 5,
    "baseStatType": "resistCritRate",
    "category": "defensive",
    "description": "Increases Resist Critical Rate",
    "location": "NPC Chloe's Request with material from World Ruler Boss",
    "valuePerLevel": [1, 2, 3, 4, 5],
    "apCost": [300, 400, 500, 600, 700],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core(Highest)", "quantity": 20 },
      { "level": 3, "name": "Upgrade Core(Highest)", "quantity": 30 },
      { "level": 4, "name": "Upgrade Core(Highest)", "quantity": 40 },
      { "level": 5, "name": "Upgrade Core(Highest)", "quantity": 50 }
    ]
  },
  {
    "id": "ignoreResistCritRate",
    "name": "Ignore Resist Critical Rate",
    "tier": 1,
    "maxLevel": 3,
    "baseStatType": "ignoreResistCritRate",
    "category": "offensive",
    "description": "Increases Ignore Resist Critical Rate",
    "location": "Forgotten Temple B3F",
    "valuePerLevel": [1, 2, 3],
    "apCost": [500, 600, 700],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 10 },
      { "level": 3, "name": "Divine Stone", "quantity": 20 }
    ]
  },
  {
    "id": "resistSkillAmp",
    "name": "Resist Skill Amp.",
    "tier": 1,
    "maxLevel": 5,
    "baseStatType": "resistSkillAmp",
    "category": "defensive",
    "description": "Increases Resist Skill Amp",
    "location": "Exchange Shop at Secret Dealer Hirogley",
    "valuePerLevel": [1, 2, 3, 4, 5],
    "apCost": [310, 312, 315, 320, 325],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 5 },
      { "level": 3, "name": "Divine Stone", "quantity": 10 },
      { "level": 4, "name": "Divine Stone", "quantity": 15 },
      { "level": 5, "name": "Divine Stone", "quantity": 20 }
    ]
  },
  {
    "id": "ignoreDamageReduction",
    "name": "Ignore Damage Reduction",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "ignoreDamageReduce",
    "category": "offensive",
    "description": "Increases Ignore Damage Reduction",
    "location": "Flame Nest",
    "valuePerLevel": [8, 16, 24, 32, 40, 48, 56, 64, 72, 80],
    "apCost": [251, 300, 349, 398, 447, 496, 545, 594, 643, 692],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 3 },
      { "level": 3, "name": "Divine Stone", "quantity": 4 },
      { "level": 4, "name": "Divine Stone", "quantity": 5 },
      { "level": 5, "name": "Divine Stone", "quantity": 6 },
      { "level": 6, "name": "Divine Stone", "quantity": 7 },
      { "level": 7, "name": "Divine Stone", "quantity": 8 },
      { "level": 8, "name": "Divine Stone", "quantity": 9 },
      { "level": 9, "name": "Divine Stone", "quantity": 10 },
      { "level": 10, "name": "Divine Stone", "quantity": 11 }
    ]
  },
  {
    "id": "resistCritDMG",
    "name": "Resist Critical DMG",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "resistCritDmg",
    "category": "defensive",
    "description": "Increases Resist Critical DMG",
    "location": "Exchange Shop at Secret Dealer Hirogley",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    "apCost": [310, 313, 316, 319, 322, 325, 328, 331, 334, 337],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 10 },
      { "level": 3, "name": "Divine Stone", "quantity": 20 },
      { "level": 4, "name": "Divine Stone", "quantity": 30 },
      { "level": 5, "name": "Divine Stone", "quantity": 40 },
      { "level": 6, "name": "Divine Stone", "quantity": 50 },
      { "level": 7, "name": "Divine Stone", "quantity": 60 },
      { "level": 8, "name": "Divine Stone", "quantity": 70 },
      { "level": 9, "name": "Divine Stone", "quantity": 80 },
      { "level": 10, "name": "Divine Stone", "quantity": 90 }
    ]
  },
  {
    "id": "ignoreAccuracy",
    "name": "Ignore Accuracy",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "ignoreAccuracy",
    "category": "offensive",
    "description": "Increases Ignore Accuracy",
    "location": "Forbidden Island (Awakening)",
    "valuePerLevel": [20, 40, 60, 80, 100, 120, 140, 160, 180, 200],
    "apCost": [80, 97, 116, 138, 164, 195, 232, 276, 328, 389],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 3 },
      { "level": 3, "name": "Divine Stone", "quantity": 4 },
      { "level": 4, "name": "Divine Stone", "quantity": 5 },
      { "level": 5, "name": "Divine Stone", "quantity": 6 },
      { "level": 6, "name": "Divine Stone", "quantity": 7 },
      { "level": 7, "name": "Divine Stone", "quantity": 8 },
      { "level": 8, "name": "Divine Stone", "quantity": 9 },
      { "level": 9, "name": "Divine Stone", "quantity": 10 },
      { "level": 10, "name": "Divine Stone", "quantity": 11 }
    ]
  },
  {
    "id": "normalDamageUp",
    "name": "Normal Damage UP",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "normalDamageUp",
    "category": "offensive",
    "description": "Increases Normal Damage",
    "location": "Acheron Arena",
    "valuePerLevel": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    "apCost": [200, 205, 210, 215, 220, 225, 230, 235, 240, 250],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Upgrade Core(Highest)", "quantity": 10 },
      { "level": 3, "name": "Upgrade Core(Highest)", "quantity": 10 },
      { "level": 4, "name": "Upgrade Core(Highest)", "quantity": 10 },
      { "level": 5, "name": "Upgrade Core(Highest)", "quantity": 15 },
      { "level": 6, "name": "Upgrade Core(Highest)", "quantity": 15 },
      { "level": 7, "name": "Upgrade Core(Highest)", "quantity": 15 },
      { "level": 8, "name": "Upgrade Core(Highest)", "quantity": 20 },
      { "level": 9, "name": "Upgrade Core(Highest)", "quantity": 20 },
      { "level": 10, "name": "Upgrade Core(Highest)", "quantity": 20 }
    ]
  },
  {
    "id": "ignoreEvasion",
    "name": "Ignore Evasion",
    "tier": 1,
    "maxLevel": 10,
    "baseStatType": "ignoreEvasion",
    "category": "offensive",
    "description": "Increases Ignore Evasion",
    "location": "Ancient Tomb",
    "valuePerLevel": [25, 50, 75, 100, 125, 150, 175, 200, 225, 250],
    "apCost": [251, 300, 349, 398, 447, 496, 545, 594, 643, 692],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 3 },
      { "level": 3, "name": "Divine Stone", "quantity": 4 },
      { "level": 4, "name": "Divine Stone", "quantity": 5 },
      { "level": 5, "name": "Divine Stone", "quantity": 6 },
      { "level": 6, "name": "Divine Stone", "quantity": 7 },
      { "level": 7, "name": "Divine Stone", "quantity": 8 },
      { "level": 8, "name": "Divine Stone", "quantity": 9 },
      { "level": 9, "name": "Divine Stone", "quantity": 10 },
      { "level": 10, "name": "Divine Stone", "quantity": 11 }
    ]
  },
  {
    "id": "ignoreResistSkillAmp",
    "name": "Ignore Resist Skill Amp.",
    "tier": 1,
    "maxLevel": 5,
    "baseStatType": "ignoreResistSkillAmp",
    "category": "offensive",
    "description": "Increases Ignore Resist Skill Amp",
    "location": "Frozen Canyon",
    "valuePerLevel": [1, 2, 3, 4, 5],
    "apCost": [310, 312, 315, 320, 325],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 5 },
      { "level": 3, "name": "Divine Stone", "quantity": 10 },
      { "level": 4, "name": "Divine Stone", "quantity": 15 },
      { "level": 5, "name": "Divine Stone", "quantity": 20 }
    ]
  },
  {
    "id": "cancelIgnorePenetration",
    "name": "Cancel Ignore Penetration",
    "tier": 1,
    "maxLevel": 9,
    "baseStatType": "cancelIgnorePenetration",
    "category": "defensive",
    "description": "Increases Cancel Ignore Penetration",
    "location": "Mirage Island (Awakening)",
    "valuePerLevel": [5, 10, 15, 20, 25, 30, 35, 40, 45],
    "apCost": [450, 475, 500, 525, 550, 575, 600, 625, 650],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 5 },
      { "level": 3, "name": "Divine Stone", "quantity": 5 },
      { "level": 4, "name": "Divine Stone", "quantity": 5 },
      { "level": 5, "name": "Divine Stone", "quantity": 10 },
      { "level": 6, "name": "Divine Stone", "quantity": 10 },
      { "level": 7, "name": "Divine Stone", "quantity": 10 },
      { "level": 8, "name": "Divine Stone", "quantity": 15 },
      { "level": 9, "name": "Divine Stone", "quantity": 20 }
    ]
  },
  {
    "id": "ignoreResistCritDmg",
    "name": "Ignore Resist Critical DMG",
    "tier": 1,
    "maxLevel": 5,
    "baseStatType": "ignoreResistCritDmg",
    "category": "offensive",
    "description": "Increases Ignore Resist Critical Damage",
    "location": "Terminus Machina",
    "valuePerLevel": [2, 4, 6, 8, 10],
    "apCost": [310, 312, 315, 320, 325],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 5 },
      { "level": 3, "name": "Divine Stone", "quantity": 10 },
      { "level": 4, "name": "Divine Stone", "quantity": 15 },
      { "level": 5, "name": "Divine Stone", "quantity": 20 }
    ]
  },

  {
    "id": "magicSkillAmp2",
    "name": "Magic Skill Amp II",
    "tier": 2,
    "maxLevel": 5,
    "baseStatType": "magicSkillAmp",
    "category": "offensive",
    "description": "Increases Magic Skill Amp (Enhanced)",
    "location": "Garden of Dust (Secret Chest)",
    "valuePerLevel": [1, 2, 3, 4, 5],
    "apCost": [300, 350, 400, 450, 500],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 5 },
      { "level": 3, "name": "Divine Stone", "quantity": 10 },
      { "level": 4, "name": "Divine Stone", "quantity": 15 },
      { "level": 5, "name": "Divine Stone", "quantity": 20 }
    ]
  },
  {
    "id": "swordSkillAmp2",
    "name": "Sword Skill Amp II",
    "tier": 2,
    "maxLevel": 5,
    "baseStatType": "swordSkillAmp",
    "category": "offensive",
    "description": "Increases Sword Skill Amp (Enhanced)",
    "location": "Garden of Dust (Secret Chest)",
    "valuePerLevel": [1, 2, 3, 4, 5],
    "apCost": [300, 350, 400, 450, 500],
    "materials": [
      { "level": 1, "name": null, "quantity": 0 },
      { "level": 2, "name": "Divine Stone", "quantity": 5 },
      { "level": 3, "name": "Divine Stone", "quantity": 10 },
      { "level": 4, "name": "Divine Stone", "quantity": 15 },
      { "level": 5, "name": "Divine Stone", "quantity": 20 }
    ]
  }
];


// Helper functions
export const getRuneById = (id: string): EssenceRune | undefined => {
  return essenceRunes.find(rune => rune.id === id);
};

export const getRunesByCategory = (category: 'offensive' | 'defensive' | 'utility'): EssenceRune[] => {
  return essenceRunes.filter(rune => rune.category === category);
};

export const getRunesByTier = (tier: number): EssenceRune[] => {
  return essenceRunes.filter(rune => rune.tier === tier);
};

export const getRunesByStatType = (statType: string): EssenceRune[] => {
  return essenceRunes.filter(rune => rune.baseStatType === statType);
};