import Link from 'next/link';
import RecentPatches from '@/components/ui/RecentPatches';

export default function HomePage() {
  return (
    <div className="min-h-screen text-foreground">
      {/* Hero Section with Background Image */}
      <div className="relative min-h-screen">
        {/* Background Image with Gradient Overlay */}
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: "linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.7) 70%, var(--theme-darkest) 100%), url('/images/homepage/home.png')",
          }}
        ></div>
        
        {/* Hero Content */}
        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <div className="text-center max-w-2xl mx-auto px-6">
            <h1 className="text-5xl sm:text-6xl font-bold text-game-gold glow-text-lg mb-4">
              Nipperlug
            </h1>
            <div className="h-1 w-24 bg-gradient-to-r from-transparent via-game-highlight to-transparent mx-auto mb-6"></div>
            <p className="text-xl sm:text-2xl text-game-platinum font-semibold glow-text-sm mb-6">
              Ultimate Cabal Online Toolkit
            </p>
            
            <p className="text-lg text-foreground/90 leading-relaxed mb-8">
              Professional-grade tools for character optimization, game analysis, and strategic planning.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link 
                href="/build-planner" 
                className="bg-game-highlight hover:bg-game-highlight/80 text-theme-darkest px-8 py-4 rounded-lg text-lg font-semibold hover:scale-105 transition-all duration-200"
              >
                Launch Build Planner
              </Link>
              <Link 
                href="/tier-lists" 
                className="glass-panel px-8 py-4 rounded-lg text-lg font-semibold text-foreground hover:text-game-highlight hover:scale-105 transition-all duration-200 border border-border-light"
              >
                View Tier Lists
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Tools Section */}
      <div className="bg-theme-darkest">
        <div className="max-w-4xl mx-auto px-6 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-game-gold mb-4">
              Professional Gaming Tools
            </h2>
            <div className="h-1 w-24 bg-gradient-to-r from-transparent via-game-highlight to-transparent mx-auto mb-6"></div>
            <p className="text-lg text-foreground/80">
              Everything you need to dominate Cabal Online, from character optimization to strategic analysis.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
            {/* Build Planner - Featured */}
            <div className="lg:col-span-2">
              <div className="glass-panel-dark p-6 h-full border-l-4 border-game-gold">
              <div className="flex items-start justify-between mb-6">
                <div>
                  <h3 className="text-2xl font-bold text-game-gold mb-2">
                    Advanced Build Planner
                  </h3>
                  <div className="flex items-center gap-2 mb-4">
                    <span className="px-3 py-1 bg-game-gold/20 text-game-gold text-sm rounded-full border border-game-gold/30">
                      Most Popular
                    </span>
                    <span className="px-3 py-1 bg-foreground/10 text-foreground/80 text-sm rounded-full border border-foreground/20">
                      16+ Systems
                    </span>
                  </div>
                </div>
              </div>
              
              <p className="text-foreground/90 leading-relaxed mb-6 text-lg">
                The most comprehensive character build optimization tool available. Integrates all major game systems 
                including equipment, skills, force wings, collections, and more for precise stat calculations.
              </p>

              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-game-highlight rounded-full"></div>
                  <span className="text-sm text-foreground/80">Real-time Damage Analysis</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-game-highlight rounded-full"></div>
                  <span className="text-sm text-foreground/80">Equipment Optimization</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-game-highlight rounded-full"></div>
                  <span className="text-sm text-foreground/80">Build Sharing & Export</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-game-gold rounded-full"></div>
                  <span className="text-sm text-foreground/80">Auto-Save Progress</span>
                </div>
              </div>

              <Link 
                href="/build-planner" 
                className="inline-flex items-center gap-2 bg-game-highlight hover:bg-game-highlight/80 text-theme-darkest px-6 py-3 rounded-lg transition-all duration-200 font-semibold text-lg hover:scale-105"
              >
                Open Build Planner
                <span className="text-xl">→</span>
              </Link>
            </div>
          </div>

            {/* Reference Tools */}
            <div className="space-y-6">
              <div className="glass-panel p-6 border-l-4 border-game-platinum">
              <h3 className="text-xl font-bold text-game-platinum mb-3">
                Reference Database
              </h3>
              <p className="text-foreground/80 mb-4 leading-relaxed">
                Comprehensive game data and tier rankings for strategic planning.
              </p>
              <div className="space-y-3">
                <Link href="/tier-lists" className="flex items-center justify-between p-3 rounded-lg bg-foreground/5 hover:bg-game-highlight/10 transition-colors group">
                  <span className="text-foreground group-hover:text-game-highlight transition-colors">Class Tier Lists</span>
                  <span className="text-game-highlight group-hover:translate-x-1 transition-transform">→</span>
                </Link>
                <Link href="/mob-table" className="flex items-center justify-between p-3 rounded-lg bg-foreground/5 hover:bg-game-highlight/10 transition-colors group">
                  <span className="text-foreground group-hover:text-game-highlight transition-colors">Monster Database</span>
                  <span className="text-game-highlight group-hover:translate-x-1 transition-transform">→</span>
                </Link>
                <Link href="/stats-wiki" className="flex items-center justify-between p-3 rounded-lg bg-foreground/5 hover:bg-game-highlight/10 transition-colors group">
                  <span className="text-foreground group-hover:text-game-highlight transition-colors">Stats Reference</span>
                  <span className="text-game-highlight group-hover:translate-x-1 transition-transform">→</span>
                </Link>
              </div>
            </div>

            <div className="glass-panel p-6 border-l-4 border-game-gold">
              <h3 className="text-xl font-bold text-game-gold mb-3">
                Calculators & Tools
              </h3>
              <p className="text-foreground/80 mb-4 leading-relaxed">
                Precision calculators for experience planning and profit optimization.
              </p>
              <div className="space-y-3">
                <Link href="/exp-calculators" className="flex items-center justify-between p-3 rounded-lg bg-foreground/5 hover:bg-game-highlight/10 transition-colors group">
                  <span className="text-foreground group-hover:text-game-highlight transition-colors">EXP Calculators</span>
                  <span className="text-game-highlight group-hover:translate-x-1 transition-transform">→</span>
                </Link>
                <Link href="/chloe-calculators" className="flex items-center justify-between p-3 rounded-lg bg-foreground/5 hover:bg-game-highlight/10 transition-colors group">
                  <span className="text-foreground group-hover:text-game-highlight transition-colors">Chloe Calculators</span>
                  <span className="text-game-highlight group-hover:translate-x-1 transition-transform">→</span>
                </Link>
              </div>
            </div>
            </div>
          </div>

          {/* Recent Updates */}
          <div className="mb-12">
            <RecentPatches />
          </div>

          {/* Quick Access Grid */}
          <div className="glass-panel-dark p-6">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-game-gold mb-2">
                Quick Access
              </h3>
              <p className="text-foreground/70">
                Jump directly to your favorite tools
              </p>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <Link href="/build-planner" className="group glass-panel p-4 rounded-lg text-center transition-all duration-200 hover:scale-105 hover:border-game-highlight">
              <div className="text-foreground font-semibold mb-1 group-hover:text-game-highlight">Build Planner</div>
              <div className="text-xs text-foreground/70 group-hover:text-foreground/90">Character Builds</div>
            </Link>
            <Link href="/tier-lists" className="group glass-panel p-4 rounded-lg text-center transition-all duration-200 hover:scale-105 hover:border-game-highlight">
              <div className="text-foreground font-semibold mb-1 group-hover:text-game-highlight">Tier Lists</div>
              <div className="text-xs text-foreground/70 group-hover:text-foreground/90">Class Rankings</div>
            </Link>
            <Link href="/exp-calculators/character" className="group glass-panel p-4 rounded-lg text-center transition-all duration-200 hover:scale-105 hover:border-game-highlight">
              <div className="text-foreground font-semibold mb-1 group-hover:text-game-highlight">Character EXP</div>
              <div className="text-xs text-foreground/70 group-hover:text-foreground/90">Level Planning</div>
            </Link>
            <Link href="/chloe-calculators/chloe-craft" className="group glass-panel p-4 rounded-lg text-center transition-all duration-200 hover:scale-105 hover:border-game-highlight">
              <div className="text-foreground font-semibold mb-1 group-hover:text-game-highlight">Chloe Craft</div>
              <div className="text-xs text-foreground/70 group-hover:text-foreground/90">Profit Analysis</div>
            </Link>
            <Link href="/mob-table" className="group glass-panel p-4 rounded-lg text-center transition-all duration-200 hover:scale-105 hover:border-game-highlight">
              <div className="text-foreground font-semibold mb-1 group-hover:text-game-highlight">Mob Database</div>
              <div className="text-xs text-foreground/70 group-hover:text-foreground/90">Monster Info</div>
            </Link>
            <Link href="/stats-wiki" className="group glass-panel p-4 rounded-lg text-center transition-all duration-200 hover:scale-105 hover:border-game-platinum">
              <div className="text-foreground font-semibold mb-1 group-hover:text-game-platinum">Stats Reference</div>
              <div className="text-xs text-foreground/70 group-hover:text-foreground/90">Game Mechanics</div>
            </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}