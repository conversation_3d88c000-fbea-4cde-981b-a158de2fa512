/* Import Tailwind base styles */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

:root {
  /* Base theme colors */
  --background: #20202c;
  --foreground: #e0e0e0;
  
  /* Theme background variations - Game-inspired dark theme */
  --theme-darkest: #0a0a0c;
  --theme-darker: #12121a;
  --theme-dark: #16161e;
  --theme-light: #1a1a24;
  --theme-lighter: #1e1e28;
  
  /* Component-specific backgrounds with glass effect */
  --component-card: rgba(18, 18, 26, 0.85);
  --component-sidebar: rgba(18, 18, 26, 0.85);
  --component-summary: rgba(18, 18, 26, 0.85);
  --component-panel: rgba(30, 30, 40, 0.7); /* Standardize panel background */
  
  /* Border colors for glass effect */
  --border-dark: rgba(40, 40, 50, 0.6);
  --border-light: rgba(80, 80, 100, 0.3);
  
  /* Stat category colors - Game-inspired */
  --stat-offensive: #ff6b6b;
  --stat-offensive-bg: rgba(255, 107, 107, 0.1);
  --stat-offensive-border: rgba(255, 107, 107, 0.3);
  
  --stat-defensive: #4dabff;
  --stat-defensive-bg: rgba(77, 171, 255, 0.1);
  --stat-defensive-border: rgba(77, 171, 255, 0.3);
  
  --stat-utility: #4dffb8;
  --stat-utility-bg: rgba(77, 255, 184, 0.1);
  --stat-utility-border: rgba(77, 255, 184, 0.3);
  
  /* Special colors */
  --gold: #ffd700;
  --gold-dark: #b39700;
  --platinum: #e5e4e2;
  --platinum-dark: #b8b6b3;
  --highlight: #ffcc00;
}

.dark {
  --background: #0a0a0c;
  --foreground: #ededed;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Enhanced Panel System */
.glass-panel {
  background: linear-gradient(135deg, rgba(18, 18, 26, 0.95), rgba(12, 12, 18, 0.98));
  border: 1px solid var(--border-dark);
  box-shadow: 
    0 4px 6px rgba(0, 0, 0, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  border-radius: 0.375rem;
}

.glass-panel-light {
  background: linear-gradient(135deg, rgba(30, 30, 40, 0.9), rgba(24, 24, 32, 0.95));
  border: 1px solid var(--border-light);
  box-shadow: 
    0 4px 6px rgba(0, 0, 0, 0.2),
    0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
  border-radius: 0.375rem;
}

.glass-panel-dark {
  background: linear-gradient(135deg, rgba(10, 10, 14, 0.95), rgba(6, 6, 10, 0.98));
  border: 1px solid var(--border-dark);
  box-shadow: 
    0 4px 6px rgba(0, 0, 0, 0.4),
    0 2px 4px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.03);
  border-radius: 0.375rem;
}

.overlord-mastery-panel {
  background: linear-gradient(135deg, rgba(18, 18, 26, 0.95), rgba(12, 12, 18, 0.98));
  border: 1px solid var(--border-dark);
  box-shadow: 
    0 4px 6px rgba(0, 0, 0, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  border-radius: 0.375rem;
}

.rune-slot-panel {
  background: linear-gradient(135deg, rgba(18, 18, 26, 0.92), rgba(14, 14, 20, 0.95));
  border: 1px solid var(--border-dark);
  box-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.2),
    0 1px 2px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.04);
  border-radius: 0.5rem;
}

.rune-system-panel {
  background: linear-gradient(135deg, rgba(18, 18, 26, 0.95), rgba(12, 12, 18, 0.98));
  border: 1px solid var(--border-dark);
  box-shadow: 
    0 4px 6px rgba(0, 0, 0, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  border-radius: 0.375rem;
}

/* Component container backgrounds */
.component-bg {
  background-color: rgba(12, 12, 18, 0.7);
  border: 1px solid var(--border-dark);
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

@media (min-width: 640px) {
  .component-bg {
    padding: 1rem;
    margin-bottom: 1rem;
  }
}

.component-bg-dark {
  background-color: rgba(8, 8, 12, 0.8);
  border: 1px solid var(--border-dark);
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

@media (min-width: 640px) {
  .component-bg-dark {
    padding: 1rem;
    margin-bottom: 1rem;
  }
}

/* Special panel for system info sections with desired color */
.system-info-panel {
  background: linear-gradient(135deg, rgba(30, 30, 40, 0.9), rgba(24, 24, 32, 0.95));
  border: 1px solid var(--border-dark);
  border-radius: 0.375rem;
  padding: 1.5rem;
  box-shadow: 
    0 4px 6px rgba(0, 0, 0, 0.4),
    0 1px 3px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

@media (min-width: 640px) {
  .system-info-panel {
    padding: 2rem;
  }
}

.component-bg-light {
  background-color: rgba(20, 20, 28, 0.7);
  border: 1px solid var(--border-light);
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.25);
}

@media (min-width: 640px) {
  .component-bg-light {
    padding: 1rem;
    margin-bottom: 1rem;
  }
}

/* Game-style header */
.game-header {
  background-color: rgba(10, 10, 14, 0.9);
  border-bottom: 1px solid var(--border-light);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Game-style button */
.game-button {
  background-color: rgba(40, 40, 60, 0.8);
  border: 1px solid var(--border-light);
  color: var(--foreground);
  transition: all 0.2s ease;
}

.game-button:hover {
  background-color: rgba(60, 60, 80, 0.8);
  border-color: var(--highlight);
}

/* Game UI specific elements */
.game-panel {
  background-color: var(--component-card);
  border: 1px solid var(--border-dark);
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.game-panel-header {
  background-color: rgba(10, 10, 14, 0.95);
  border-bottom: 1px solid var(--border-light);
  padding: 0.5rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Simplified game slot - only essential base styles */
.game-slot {
  background-color: rgba(20, 20, 30, 0.7);
  border: 1px solid var(--border-dark);
}

/* Essential shadow glow for active items */
.shadow-glow {
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.4);
}

.game-progress-bar {
  height: 8px;
  background-color: rgba(30, 30, 40, 0.7);
  border: 1px solid var(--border-dark);
  border-radius: 4px;
  overflow: hidden;
}

.game-progress-fill {
  height: 100%;
  background-color: var(--highlight);
  transition: width 0.3s ease;
}

.glow-text {
  text-shadow: 0 0 5px currentColor;
}

.glow-text-sm {
  text-shadow: 0 0 3px currentColor;
}

.glow-text-md {
  text-shadow: 0 0 5px currentColor;
}

.glow-text-lg {
  text-shadow: 0 0 8px currentColor;
}

.glow-border {
  box-shadow: 0 0 8px currentColor;
}

/* Component border styles */
.border-game-highlight {
  border-color: var(--highlight);
}

.border-game-gold {
  border-color: var(--gold);
}

.border-game-platinum {
  border-color: var(--platinum);
}

/* Component background with patterns */
.bg-pattern-grid {
  background-image: linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

.bg-pattern-diagonal {
  background-image: repeating-linear-gradient(45deg, rgba(255, 255, 255, 0.03), rgba(255, 255, 255, 0.03) 10px, transparent 10px, transparent 20px);
}

/* Dark scrollbar styling - Keep as custom since Tailwind doesn't handle scrollbars well */
.dark-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--border-dark) var(--theme-light);
}

.dark-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.dark-scrollbar::-webkit-scrollbar-track {
  background: var(--theme-light);
  border-radius: 4px;
}

.dark-scrollbar::-webkit-scrollbar-thumb {
  background-color: var(--border-dark);
  border-radius: 4px;
}

.dark-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: var(--border-light);
}

/* Enhanced button styles */
/* Button container */
.glass-button-group {
  background: linear-gradient(135deg, rgba(20, 20, 30, 0.9), rgba(16, 16, 24, 0.95));
  border: 1px solid var(--border-light);
  border-radius: 0.75rem;
  box-shadow: 
    0 4px 8px rgba(0, 0, 0, 0.25),
    0 2px 4px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(8px);
}

/* Stat category buttons - Used in BuildSummary tabs */
.glass-button-offensive {
  background: linear-gradient(135deg, var(--stat-offensive-bg), rgba(255, 107, 107, 0.15));
  border: 1px solid var(--stat-offensive-border);
  box-shadow: 
    0 2px 4px rgba(255, 107, 107, 0.1),
    inset 0 1px 0 rgba(255, 107, 107, 0.1);
}

.glass-button-defensive {
  background: linear-gradient(135deg, var(--stat-defensive-bg), rgba(77, 171, 255, 0.15));
  border: 1px solid var(--stat-defensive-border);
  box-shadow: 
    0 2px 4px rgba(77, 171, 255, 0.1),
    inset 0 1px 0 rgba(77, 171, 255, 0.1);
}

.glass-button-utility {
  background: linear-gradient(135deg, var(--stat-utility-bg), rgba(77, 255, 184, 0.15));
  border: 1px solid var(--stat-utility-border);
  box-shadow: 
    0 2px 4px rgba(77, 255, 184, 0.1),
    inset 0 1px 0 rgba(77, 255, 184, 0.1);
}

/* Action buttons - Used in build-planner page */
.glass-button-blue {
  background: linear-gradient(135deg, rgba(77, 171, 255, 0.25), rgba(77, 171, 255, 0.15));
  border: 1px solid rgba(77, 171, 255, 0.4);
  box-shadow: 
    0 2px 4px rgba(77, 171, 255, 0.1),
    inset 0 1px 0 rgba(77, 171, 255, 0.1);
}

.glass-button-green {
  background: linear-gradient(135deg, rgba(77, 255, 184, 0.25), rgba(77, 255, 184, 0.15));
  border: 1px solid rgba(77, 255, 184, 0.4);
  box-shadow: 
    0 2px 4px rgba(77, 255, 184, 0.1),
    inset 0 1px 0 rgba(77, 255, 184, 0.1);
}

.glass-button-purple {
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.25), rgba(147, 51, 234, 0.15));
  border: 1px solid rgba(147, 51, 234, 0.4);
  box-shadow: 
    0 2px 4px rgba(147, 51, 234, 0.1),
    inset 0 1px 0 rgba(147, 51, 234, 0.1);
}

.glass-button-orange {
  background: linear-gradient(135deg, rgba(255, 165, 0, 0.25), rgba(255, 165, 0, 0.15));
  border: 1px solid rgba(255, 165, 0, 0.4);
  box-shadow: 
    0 2px 4px rgba(255, 165, 0, 0.1),
    inset 0 1px 0 rgba(255, 165, 0, 0.1);
}

.glass-button-red {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.25), rgba(255, 107, 107, 0.15));
  border: 1px solid rgba(255, 107, 107, 0.4);
  box-shadow: 
    0 2px 4px rgba(255, 107, 107, 0.1),
    inset 0 1px 0 rgba(255, 107, 107, 0.1);
}

/* Button hover effect */
.glass-button-hover {
  background-color: rgba(60, 60, 80, 0.8);
  border-color: var(--border-light);
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.2);
}

/* Mythical Level System Styles */
.mythical-node {
  cursor: pointer;
  transition: all 0.2s ease;
}

.mythical-node:hover {
  transform: scale(1.05);
}

.mythical-node-active {
  box-shadow: 0 0 8px rgba(255, 215, 0, 0.6);
}

/* Text shadow utility for collection system */
.text-shadow-lg {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

/* Range slider styles */
.range-lg-game {
  -webkit-appearance: none;
  appearance: none;
}

.range-lg-game::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: var(--highlight);
  cursor: pointer;
  border: 2px solid var(--gold);
}

.range-lg-game::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: var(--highlight);
  cursor: pointer;
  border: 2px solid var(--gold);
}

.range-lg-game::-webkit-slider-track {
  height: 8px;
  border-radius: 4px;
  background: rgba(30, 30, 40, 0.7);
  border: 1px solid var(--border-dark);
}

.range-lg-game::-moz-range-track {
  height: 8px;
  border-radius: 4px;
  background: rgba(30, 30, 40, 0.7);
  border: 1px solid var(--border-dark);
}

/* Tailwind-compatible utility classes */
@layer utilities {
  /* Essential glass effect */
  .glass-effect {
    background: rgba(18, 18, 26, 0.95);
    border: 1px solid var(--border-dark);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  }
  
  /* Essential shadow utilities */
  .shadow-game {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  }
  
  /* Cascading dropdown animation */
  .animate-slideDown {
    animation: slideDown 0.2s ease-out forwards;
  }
  
  @keyframes slideDown {
    from {
      opacity: 0;
      max-height: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      max-height: 500px;
      transform: translateY(0);
    }
  }

  /* Profit status classes for Chloe Calculator */
  .profit-negative {
    color: #ef4444; /* red-500 */
  }
  
  .profit-low {
    color: #eab308; /* yellow-500 */
  }
  
  .profit-medium {
    color: #3b82f6; /* blue-500 */
  }
  
  .profit-high {
    color: #10b981; /* emerald-500 */
  }
}
