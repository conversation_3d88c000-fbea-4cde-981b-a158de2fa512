/**
 * Devil Shop Calculator - Enhanced Table-based UI
 * Migrated from WordPress implementation with modern React patterns
 */

'use client';

import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { DEVIL_SHOP_ITEMS, type DevilShopItem } from '../data/items';
import { usePriceStore } from '@/stores/priceStore';
import DevilShopFilters from './DevilShopFilters';
import ImportPricesModal from './ImportPricesModal';

interface SortConfig {
  key: keyof DevilShopItem | 'profitPerPurchase' | 'profitPerToken' | 'tokenCost' | 'totalValue';
  direction: 'asc' | 'desc';
}

export default function DevilShopCalculator() {
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [tokenTypeFilter, setTokenTypeFilter] = useState<string>('');
  const [showOnlyFavorites, setShowOnlyFavorites] = useState(false);
  const [excludeMissingPrices, setExcludeMissingPrices] = useState(false);
  const [salesFee, setSalesFee] = useState(5);
  const [sortConfig, setSortConfig] = useState<SortConfig | null>({
    key: 'profitPerToken',
    direction: 'desc'
  });
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [importModal, setImportModal] = useState(false);
  
  const { getPrice, setPrice, clearAllPrices } = usePriceStore();

  useEffect(() => {
    setMounted(true);
  }, []);

  // Get token price from global price store
  const getTokenPrice = useCallback((tokenType: 'High' | 'Highest'): number => {
    return getPrice(`Devil's Token(${tokenType})`) || 0;
  }, [getPrice]);

  // Calculate profit for an item (per purchase)
  const calculateProfit = useCallback((item: DevilShopItem): number => {
    const marketPrice = getPrice(item.name) || 0;
    const tokenPrice = getTokenPrice(item.tokenType);
    
    // Apply sales fee to the market price
    const adjustedMarketPrice = marketPrice * (1 - salesFee / 100);
    
    const totalMarketValue = adjustedMarketPrice * item.quantity;
    const totalTokenCost = item.tokensRequired * tokenPrice;
    
    return totalMarketValue - totalTokenCost;
  }, [getPrice, getTokenPrice, salesFee]);

  // Calculate profit per token (for better comparison across items)
  const calculateProfitPerToken = useCallback((item: DevilShopItem): number => {
    const totalProfit = calculateProfit(item);
    return item.tokensRequired > 0 ? totalProfit / item.tokensRequired : 0;
  }, [calculateProfit]);

  // Calculate token cost for an item
  const calculateTokenCost = useCallback((item: DevilShopItem): number => {
    const tokenPrice = getTokenPrice(item.tokenType);
    return item.tokensRequired * tokenPrice;
  }, [getTokenPrice]);

  // Calculate total value for an item
  const calculateTotalValue = useCallback((item: DevilShopItem): number => {
    const marketPrice = getPrice(item.name) || 0;
    return marketPrice * item.quantity;
  }, [getPrice]);

  // Filter and sort items
  const filteredAndSortedItems = useMemo(() => {
    let filtered = DEVIL_SHOP_ITEMS.filter(item => {
      const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesTokenType = !tokenTypeFilter || item.tokenType === tokenTypeFilter;
      const matchesFavorites = !showOnlyFavorites || favorites.has(item.name);
      
      // Check for missing prices
      let matchesPriceFilter = true;
      if (excludeMissingPrices) {
        const hasMarketPrice = (getPrice(item.name) || 0) > 0;
        const hasTokenPrice = getTokenPrice(item.tokenType) > 0;
        matchesPriceFilter = hasMarketPrice && hasTokenPrice;
      }
      
      return matchesSearch && matchesTokenType && matchesFavorites && matchesPriceFilter;
    });

    if (sortConfig) {
      filtered.sort((a, b) => {
        let aValue: number | string;
        let bValue: number | string;

        if (sortConfig.key === 'profitPerPurchase') {
          aValue = calculateProfit(a);
          bValue = calculateProfit(b);
        } else if (sortConfig.key === 'profitPerToken') {
          aValue = calculateProfitPerToken(a);
          bValue = calculateProfitPerToken(b);
        } else if (sortConfig.key === 'tokenCost') {
          aValue = calculateTokenCost(a);
          bValue = calculateTokenCost(b);
        } else if (sortConfig.key === 'totalValue') {
          aValue = calculateTotalValue(a);
          bValue = calculateTotalValue(b);
        } else {
          aValue = a[sortConfig.key] as number | string;
          bValue = b[sortConfig.key] as number | string;
        }

        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortConfig.direction === 'asc' 
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }

        const numA = Number(aValue) || 0;
        const numB = Number(bValue) || 0;
        
        return sortConfig.direction === 'asc' ? numA - numB : numB - numA;
      });
    }

    return filtered;
  }, [DEVIL_SHOP_ITEMS, searchTerm, tokenTypeFilter, showOnlyFavorites, excludeMissingPrices, favorites, sortConfig, calculateProfit, calculateProfitPerToken, calculateTokenCost, calculateTotalValue, getPrice, getTokenPrice]);

  const handleSort = (key: SortConfig['key']) => {
    setSortConfig(current => ({
      key,
      direction: current?.key === key && current.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const toggleFavorite = (itemName: string) => {
    setFavorites(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemName)) {
        newSet.delete(itemName);
      } else {
        newSet.add(itemName);
      }
      return newSet;
    });
  };

  const getSortIcon = (key: SortConfig['key']) => {
    if (sortConfig?.key !== key) return '↕️';
    return sortConfig.direction === 'asc' ? '↑' : '↓';
  };

  const formatNumber = (num: number): string => {
    return Math.round(num).toLocaleString();
  };

  const openImportModal = () => {
    setImportModal(true);
  };

  const closeImportModal = () => {
    setImportModal(false);
  };

  if (!mounted) {
    return (
      <div className="min-h-screen bg-theme-darkest text-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-2xl mb-2">Loading...</div>
          <div className="text-gray-400">Initializing Devil Shop Calculator</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-theme-darkest text-white">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="bg-component-card border border-border-dark rounded-lg p-6">
          <h1 className="text-3xl font-bold mb-6">Devil Shop Calculator</h1>
          
          {/* Description Section */}
          <div className="space-y-4 text-gray-300">
            <div>
              <h2 className="text-xl font-semibold text-white mb-2">What is this tool?</h2>
              <p>
                The Devil Shop Calculator helps you analyze the profitability of items available through 
                Devil's Token shops in Cabal Online. It calculates expected profits based on token costs 
                and current market prices to help you make informed purchasing decisions.
              </p>
            </div>

            <div>
              <h2 className="text-xl font-semibold text-white mb-2">How to use</h2>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li><strong>Set token prices:</strong> Enter current market prices for High and Highest tokens</li>
                <li><strong>Set item prices:</strong> Enter current market prices in the "Market Price" column</li>
                <li><strong>Use filters:</strong> Search by name, filter by token type, or show only profitable items</li>
                <li><strong>Sort columns:</strong> Click column headers to sort by profit, cost, or other metrics</li>
                <li><strong>Favorite items:</strong> Click the ♥ icon to mark items for easy access</li>
              </ul>
            </div>

            <div>
              <h2 className="text-xl font-semibold text-white mb-2">Understanding Profit Metrics</h2>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li><strong>Profit Per Purchase:</strong> Total profit from buying one complete shop entry (e.g., 127 items for 10 tokens)</li>
                <li><strong>Profit Per Token:</strong> Profit efficiency per token spent - better for comparing items with different token costs</li>
                <li><strong>Tip:</strong> Sort by "Profit Per Token" to find the most efficient token investments</li>
              </ul>
            </div>

            <div className="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
              <h2 className="text-xl font-semibold text-yellow-400 mb-2">⚠️ Important Notes</h2>
              <ul className="list-disc list-inside space-y-1 ml-4 text-yellow-200">
                <li><strong>Market prices change:</strong> Update prices regularly as market conditions fluctuate</li>
                <li><strong>Sales fee applied:</strong> The calculator accounts for market transaction fees (default 5%)</li>
                <li><strong>Token availability:</strong> Consider how easy it is to obtain the required tokens</li>
                <li><strong>Demand matters:</strong> High-profit items might be hard to sell if there's no demand</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Controls */}
        <DevilShopFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          tokenTypeFilter={tokenTypeFilter}
          setTokenTypeFilter={setTokenTypeFilter}
          salesFee={salesFee}
          setSalesFee={setSalesFee}
          showOnlyFavorites={showOnlyFavorites}
          setShowOnlyFavorites={setShowOnlyFavorites}
          excludeMissingPrices={excludeMissingPrices}
          setExcludeMissingPrices={setExcludeMissingPrices}
          getTokenPrice={getTokenPrice}
          setPrice={setPrice}
          clearAllPrices={clearAllPrices}
          onImportPrices={openImportModal}
        />

        {/* Results Table */}
        <div className="bg-component-card border border-border-dark rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-theme-dark border-b border-border-dark">
                <tr>
                  <th className="px-4 py-3 text-left">⭐</th>
                  <th 
                    className="px-4 py-3 text-left cursor-pointer hover:bg-theme-light"
                    onClick={() => handleSort('name')}
                  >
                    Item Name {getSortIcon('name')}
                  </th>
                  <th className="px-4 py-3 text-left">Quantity</th>
                  <th 
                    className="px-4 py-3 text-left cursor-pointer hover:bg-theme-light"
                    onClick={() => handleSort('tokenType')}
                  >
                    Token Type {getSortIcon('tokenType')}
                  </th>
                  <th 
                    className="px-4 py-3 text-left cursor-pointer hover:bg-theme-light"
                    onClick={() => handleSort('tokensRequired')}
                  >
                    Tokens Required {getSortIcon('tokensRequired')}
                  </th>
                  <th className="px-4 py-3 text-left">Market Price (per piece)</th>
                  <th 
                    className="px-4 py-3 text-left cursor-pointer hover:bg-theme-light"
                    onClick={() => handleSort('profitPerPurchase')}
                  >
                    Profit Per Purchase {getSortIcon('profitPerPurchase')}
                  </th>
                  <th 
                    className="px-4 py-3 text-left cursor-pointer hover:bg-theme-light"
                    onClick={() => handleSort('profitPerToken')}
                  >
                    Profit Per Token {getSortIcon('profitPerToken')}
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredAndSortedItems.map((item) => {
                  const isFavorite = favorites.has(item.name);
                  const profit = calculateProfit(item);
                  const profitPerToken = calculateProfitPerToken(item);
                  const tokenCost = calculateTokenCost(item);
                  const marketPrice = getPrice(item.name) || 0;
                  const hasTokenPrice = getTokenPrice(item.tokenType) > 0;

                  return (
                    <tr 
                      key={item.name}
                      className="border-b border-border-dark hover:bg-theme-light transition-colors"
                    >
                      <td className="px-4 py-3">
                        <button
                          onClick={() => toggleFavorite(item.name)}
                          className={`text-lg ${isFavorite ? 'text-red-500' : 'text-gray-400'} hover:text-red-400`}
                        >
                          ♥
                        </button>
                      </td>
                      <td className="px-4 py-3">
                        <span className="font-medium">{item.name}</span>
                      </td>
                      <td className="px-4 py-3 text-blue-400">{item.quantity}</td>
                      <td className="px-4 py-3">
                        <div className="flex items-center justify-center">
                          <img 
                            src={`/images/devil-shop/devil-token-${item.tokenType.toLowerCase()}.png`}
                            alt={`Devil's Token (${item.tokenType})`}
                            className="w-8 h-8"
                            title={`Devil's Token (${item.tokenType})`}
                          />
                        </div>
                      </td>
                      <td className="px-4 py-3 text-yellow-400">{item.tokensRequired}</td>
                      <td className="px-4 py-3">
                        <input
                          type="number"
                          value={marketPrice || ''}
                          onChange={(e) => {
                            setPrice(item.name, Number(e.target.value) || 0);
                          }}
                          className="w-24 bg-theme-dark border border-border-dark rounded px-2 py-1 text-white text-sm focus:border-blue-500 focus:outline-none [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-inner-spin-button]:m-0 [&::-webkit-outer-spin-button]:m-0 [-moz-appearance:textfield]"
                          placeholder="0"
                        />
                      </td>
                      <td className={`px-4 py-3 font-medium ${profit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {formatNumber(profit)}
                        {(!hasTokenPrice || marketPrice === 0) && (
                          <span 
                            className="ml-2 text-yellow-400" 
                            title="Missing token price or market price - calculations may be inaccurate"
                          >
                            ⚠️
                          </span>
                        )}
                      </td>
                      <td className={`px-4 py-3 font-medium ${profitPerToken >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {formatNumber(profitPerToken)}
                        {(!hasTokenPrice || marketPrice === 0) && (
                          <span 
                            className="ml-2 text-yellow-400" 
                            title="Missing token price or market price - calculations may be inaccurate"
                          >
                            ⚠️
                          </span>
                        )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
          
          {filteredAndSortedItems.length === 0 && (
            <div className="text-center py-8 text-gray-400">
              No items found matching your criteria.
            </div>
          )}
        </div>

        {/* Import Prices Modal */}
        <ImportPricesModal
          isOpen={importModal}
          onClose={closeImportModal}
        />
      </div>
    </div>
  );
}